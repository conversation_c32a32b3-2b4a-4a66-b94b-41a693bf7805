import { GET, POST, DELETE, PUT } from '@/web/common/api/request';
import type { EditApiKeyProps, GetApiKeyProps } from '@/global/support/openapi/api.d';
import type { OpenApiSchema } from '@/packages/global/support/openapi/type';

/**
 * crete a api key
 */
export const createAOpenApiKey = (data: EditApiKeyProps) =>
  POST<string>('/api/support/openapi/create', data);

/**
 * update a api key
 */
export const putOpenApiKey = (data: EditApiKeyProps & { _id: string }) =>
  PUT<string>('/api/support/openapi/update', data);

/**
 * get api keys
 */
export const getOpenApiKeys = (params?: GetApiKeyProps) =>
  GET<OpenApiSchema[]>('/api/support/openapi/list', params);

/**
 * delete api by id
 */
export const delOpenApiById = (id: string) => DELETE(`/api/support/openapi/delete`, { id });
