import { GET, POST } from '@/web/common/api/request';
import type { PromotionRecordType } from '@/global/support/api/userRes.d';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';

/* get promotion init data */
export const getPromotionInitData = () =>
  GET<{
    invitedAmount: number;
    earningsAmount: number;
  }>('/api/support/activity/promotion/getPromotionData');

/* promotion records */
export const getPromotionRecords = (data: PaginationProps) =>
  POST<PaginationResponse<PromotionRecordType>>(
    `/api/support/activity/promotion/getPromotions`,
    data
  );
