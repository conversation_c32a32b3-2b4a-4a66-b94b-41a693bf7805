import { GET, POST } from '@/web/common/api/request';
import type { UserInformType } from '@/packages/global/support/user/inform/type';
import type { SystemMsgModalValueType } from '@fastgpt/service/support/user/inform/type';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';

export const getInforms = (data: PaginationProps) =>
  POST<PaginationResponse<UserInformType>>(`/api/support/user/inform/list`, data);

export const getUnreadCount = () =>
  GET<{
    unReadCount: number;
    importantInforms: UserInformType[];
  }>(`/api/support/user/inform/countUnread`);
export const readInform = (id: string) => GET(`/api/support/user/inform/read`, { id });

export const getSystemMsgModalData = () =>
  GET<SystemMsgModalValueType>(`/api/support/user/inform/getSystemMsgModal`);
