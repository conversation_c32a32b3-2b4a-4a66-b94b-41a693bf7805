import { DELETE, GET, POST, PUT } from '@/web/common/api/request';
import { type GetGroupListBody } from '@/packages/global/support/permission/memberGroup/api';
import type {
  GroupMemberItemType,
  MemberGroupListItemType
} from '@/packages/global/support/permission/memberGroup/type';
import type {
  postCreateGroupData,
  putUpdateGroupData
} from '@/packages/global/support/user/team/group/api';

export const getGroupList = <T extends boolean>(data: GetGroupListBody) =>
  POST<MemberGroupListItemType<T>[]>('/api/support/user/team/group/list', data).then((res) => {
    console.log(res);
    return res;
  });

export const postCreateGroup = (data: postCreateGroupData) =>
  POST('/api/support/user/team/group/create', data);

export const deleteGroup = (groupId: string) =>
  DELETE('/api/support/user/team/group/delete', { groupId });

export const putUpdateGroup = (data: putUpdateGroupData) =>
  PUT('/api/support/user/team/group/update', data);

export const getGroupMembers = (groupId: string) =>
  GET<GroupMemberItemType[]>(`/api/support/user/team/group/members`, { groupId });

export const putGroupChangeOwner = (groupId: string, tmbId: string) =>
  PUT(`/api/support/user/team/group/changeOwner`, { groupId, tmbId });
