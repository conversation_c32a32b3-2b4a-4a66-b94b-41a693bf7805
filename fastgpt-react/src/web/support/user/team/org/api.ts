import { DELETE, GET, POST, PUT } from '@/web/common/api/request';
import type {
  postCreateOrgData,
  putUpdateOrgData,
  putUpdateOrgMembersData
} from '@/packages/global/support/user/team/org/api';
import type { OrgListItemType } from '@/packages/global/support/user/team/org/type';
import type { putMoveOrgType } from '@/packages/global/support/user/team/org/api';
import { type PaginationProps, type PaginationResponse } from '@/packages/common/fetch/type';
import { type TeamMemberItemType } from '@/packages/global/support/user/team/type';
import { type ParentIdType } from '@/packages/global/common/parentFolder/type';

export const getOrgList = (params: {
  orgId: string;
  withPermission?: boolean;
  searchKey?: string;
}) => POST<OrgListItemType[]>(`/api/support/user/team/org/list`, params);

export const postCreateOrg = (data: postCreateOrgData) =>
  POST('/api/support/user/team/org/create', data);

export const deleteOrg = (orgId: string) =>
  DELETE('/api/support/user/team/org/delete', { orgId });

export const putMoveOrg = (data: putMoveOrgType) => PUT('/api/support/user/team/org/move', data);

export const putUpdateOrg = (data: putUpdateOrgData) =>
  PUT('/api/support/user/team/org/update', data);

// org members
export const putUpdateOrgMembers = (data: putUpdateOrgMembersData) =>
  PUT('/api/support/user/team/org/updateMembers', data);

export const getOrgMembers = (data: PaginationProps<{ orgPath?: string }>) =>
  GET<PaginationResponse<TeamMemberItemType>>(`/api/support/user/team/org/members`, data);

export const deleteOrgMember = (orgId: string, tmbId: string) =>
  DELETE('/api/support/user/team/org/deleteMember', { orgId, tmbId });
