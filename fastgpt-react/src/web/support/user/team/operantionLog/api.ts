import { GET, POST, PUT } from '@/web/common/api/request';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';
import type { OperationListItemType } from '@/packages/global/support/user/audit/type';
import type { AuditEventEnum } from '@/packages/global/support/user/audit/constants';

export const getOperationLogs = (
  props: PaginationProps & {
    tmbIds?: string[];
    events?: AuditEventEnum[];
  }
) => POST<PaginationResponse<OperationListItemType>>(`/api/support/user/audit/list`, props);
