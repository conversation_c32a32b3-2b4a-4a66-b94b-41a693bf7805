import { GET, POST, PUT, DELETE } from '@/web/common/api/request';
import type {
  CollaboratorItemType,
  DeletePermissionQuery,
  UpdateClbPermissionProps
} from '@/packages/global/support/permission/collaborator';
import type {
  CreateTeamProps,
  UpdateInviteProps,
  UpdateTeamProps
} from '@/packages/global/support/user/team/controller.d';
import type { TeamTagItemType, TeamTagSchema } from '@/packages/global/support/user/team/type';
import type {
  TeamTmbItemType,
  TeamMemberItemType,
  TeamMemberSchema
} from '@/packages/global/support/user/team/type.d';
import type {
  ClientTeamPlanStatusType,
  TeamSubSchema
} from '@/packages/global/support/wallet/sub/type';
import type { TeamInvoiceHeaderType } from '@/packages/global/support/user/team/type';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';
// import type {
//   InvitationInfoType,
//   InvitationLinkCreateType,
//   InvitationType
// } from '@fastgpt/service/support/user/team/invitationLink/type';
type InvitationLinkExpiresType = '30m' | '7d' | '1y';
type InvitationSchemaType = {
    _id: string;
    linkId: string;
    teamId: string;
    usedTimesLimit?: number;
    forbidden?: boolean;
    expires: Date;
    description: string;
    members: string[];
};
type InvitationType = Omit<InvitationSchemaType, 'members'> & {
    members: {
        tmbId: string;
        avatar: string;
        name: string;
    }[];
};
type InvitationLinkCreateType = {
    description: string;
    expires: InvitationLinkExpiresType;
    usedTimesLimit: 1 | -1;
};
type InvitationInfoType = InvitationSchemaType & {
    teamAvatar: string;
    teamName: string;
};

/* --------------- team  ---------------- */
export const getTeamList = (status: `${TeamMemberSchema['status']}`) =>
  GET<TeamTmbItemType[]>(`/api/support/user/team/list`, { status });
export const postCreateTeam = (data: CreateTeamProps) =>
  POST<string>(`/api/support/user/team/create`, data);
export const putUpdateTeam = (data: UpdateTeamProps) => PUT(`/api/support/user/team/update`, data);
export const putSwitchTeam = (teamId: string) =>
  PUT<string>(`/api/support/user/team/switch`, { teamId });

/* --------------- team member ---------------- */
export const getTeamMembers = (
  props: PaginationProps<{
    status?: 'active' | 'inactive';
    withOrgs?: boolean;
    withPermission?: boolean;
    searchKey?: string;
    orgId?: string;
    groupId?: string;
  }>
) => POST<PaginationResponse<TeamMemberItemType>>(`/api/support/user/team/member/list`, props);
export const getTeamMemberCount = () =>
  GET<{ count: number }>(`/api/support/user/team/member/count`);

// export const postInviteTeamMember = (data: InviteMemberProps) =>
//   POST<InviteMemberResponse>(`/support/user/team/member/invite`, data);
export const putUpdateMemberNameByManager = (tmbId: string, name: string) =>
  PUT(`/api/support/user/team/member/updateNameByManager`, { tmbId, name });

export const putUpdateMemberName = (name: string) =>
  PUT(`/api/support/user/team/member/updateName`, { name });
export const delRemoveMember = (tmbId: string) =>
  DELETE(`/api/support/user/team/member/delete`, { tmbId });
export const updateInviteResult = (data: UpdateInviteProps) =>
  PUT('/api/support/user/team/member/updateInvite', data);
export const postRestoreMember = (tmbId: string) =>
  POST('/api/support/user/team/member/restore', { tmbId });
export const delLeaveTeam = () => DELETE('/api/support/user/team/member/leave');

/* -------------- team invitaionlink -------------------- */

export const postCreateInvitationLink = (data: InvitationLinkCreateType) =>
  POST<string>(`/api/support/user/team/invitationLink/create`, data);

export const getInvitationLinkList = () =>
  GET<InvitationType[]>(`/api/support/user/team/invitationLink/list`);

export const postAcceptInvitationLink = (linkId: string) =>
  POST<string>(`/api/support/user/team/invitationLink/accept`, { linkId });

export const getInvitationInfo = (linkId: string) =>
  GET<InvitationInfoType>(`/api/support/user/team/invitationLink/info`, { linkId });
export const putForbidInvitationLink = (linkId: string) =>
  PUT<string>(`/api/support/user/team/invitationLink/forbid`, { linkId });

/* -------------- team collaborator -------------------- */
export const getTeamClbs = () =>
  GET<CollaboratorItemType[]>(`/api/support/user/team/collaborator/list`);
export const updateMemberPermission = (data: UpdateClbPermissionProps) =>
  PUT('/api/support/user/team/collaborator/update', data);
export const deleteMemberPermission = (id: DeletePermissionQuery) =>
  DELETE('/api/support/user/team/collaborator/delete', id);

/* --------------- team tags ---------------- */
export const getTeamsTags = () => GET<TeamTagSchema[]>(`/api/support/user/team/tag/list`);
export const loadTeamTagsByDomain = (domain: string) =>
  GET<TeamTagItemType[]>(`/api/support/user/team/tag/async`, { domain });

/* team limit */
export const checkTeamExportDatasetLimit = (datasetId: string) =>
  GET(`/api/support/user/team/limit/exportDatasetLimit`, { datasetId });
export const checkTeamWebSyncLimit = () => GET(`/api/support/user/team/limit/webSyncLimit`);
export const checkTeamDatasetSizeLimit = (size: number) =>
  GET(`/api/support/user/team/limit/datasetSizeLimit`, { size });

/* plans */
export const getTeamPlanStatus = () =>
  GET<ClientTeamPlanStatusType>(`/api/support/user/team/plan/getTeamPlanStatus`, { maxQuantity: 1 });
export const getTeamPlans = () =>
  GET<TeamSubSchema[]>(`/api/support/user/team/plan/getTeamPlans`);

export const redeemCoupon = (couponCode: string) =>
  GET(`/api/support/wallet/coupon/redeem`, { key: couponCode });

export const getTeamInvoiceHeader = () =>
  GET<TeamInvoiceHeaderType>(`/api/support/user/team/invoiceAccount/getTeamInvoiceHeader`);

export const updateTeamInvoiceHeader = (data: TeamInvoiceHeaderType) =>
  POST(`/api/support/user/team/invoiceAccount/update`, data);
