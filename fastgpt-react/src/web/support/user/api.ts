import { GET, POST, PUT } from '@/web/common/api/request';
import { hashStr } from '@/packages/global/common/string/tools';
import type { ResLogin } from '@/global/support/api/userRes.d';
import type { UserAuthTypeEnum } from '@/packages/global/support/user/auth/constants';
import type { UserUpdateParams } from '@/types/user';
import type { UserType } from '@/packages/global/support/user/type.d';
import type {
  FastLoginProps,
  OauthLoginProps,
  PostLoginProps,
  SearchResult
} from '@/packages/global/support/user/api.d';
import type {
  AccountRegisterBody,
  GetWXLoginQRResponse
} from '@/packages/global/support/user/login/api.d';
import type { preLoginResponse } from '@/pages/api/support/user/account/preLogin';

export const sendAuthCode = (data: {
  username: string;
  type: `${UserAuthTypeEnum}`;
  googleToken: string;
  captcha: string;
}) => POST(`/api/support/user/inform/sendAuthCode`, data);

export const getTokenLogin = () =>
  GET<UserType>('/api/support/user/account/tokenLogin', {}, {});
export const oauthLogin = (params: OauthLoginProps) =>
  POST<ResLogin>('/api/support/user/account/login/oauth', params);
export const postFastLogin = (params: FastLoginProps) =>
  POST<ResLogin>('/api/support/user/account/login/fastLogin', params);
export const ssoLogin = (params: any) => GET<ResLogin>('/api/support/user/account/sso', params);

export const postRegister = ({
  username,
  password,
  code,
  inviterId,
  bd_vid,
  fastgpt_sem
}: AccountRegisterBody) =>
  POST<ResLogin>(`/api/support/user/account/register/emailAndPhone`, {
    username,
    code,
    inviterId,
    bd_vid,
    fastgpt_sem,
    password: hashStr(password)
  });

export const postFindPassword = ({
  username,
  code,
  password
}: {
  username: string;
  code: string;
  password: string;
}) =>
  POST<ResLogin>(`/api/support/user/account/password/updateByCode`, {
    username,
    code,
    password: hashStr(password)
  });

export const updatePasswordByOld = ({ oldPsw, newPsw }: { oldPsw: string; newPsw: string }) =>
  POST('/api/support/user/account/updatePasswordByOld', {
    oldPsw: hashStr(oldPsw),
    newPsw: hashStr(newPsw)
  });

export const resetPassword = (newPsw: string) =>
  POST('/api/support/user/account/resetExpiredPsw', {
    newPsw: hashStr(newPsw)
  });

/* Check the whether password has expired */
export const getCheckPswExpired = () => GET<boolean>('/api/support/user/account/checkPswExpired');

export const updateNotificationAccount = (data: { account: string; verifyCode: string }) =>
  PUT('/api/support/user/team/updateNotificationAccount', data);

export const updateContact = (data: { contact: string; verifyCode: string }) => {
  return PUT('/api/support/user/account/updateContact', data);
};

export const postLogin = ({ password, ...props }: PostLoginProps) =>
  POST<ResLogin>('/api/support/user/account/loginByPassword', {
    ...props,
    password: hashStr(password)
  });

export const loginOut = () => GET('/api/support/user/account/loginout');

export const putUserInfo = (data: UserUpdateParams) => PUT('/api/support/user/account/update', data);

export const getWXLoginQR = () =>
  GET<GetWXLoginQRResponse>('/api/support/user/account/login/wx/getQR');

export const getWXLoginResult = (code: string) =>
  GET<ResLogin>(`/api/support/user/account/login/wx/getResult`, { code });

export const getCaptchaPic = (username: string) =>
  GET<{
    captchaImage: string;
  }>('/api/support/user/account/captcha/getImgCaptcha', { username });

export const getPreLogin = (username: string) =>
  GET<preLoginResponse>('/api/support/user/account/preLogin', { username }, { withCredentials: false });

export const postSyncMembers = () => POST('/api/support/user/sync');

export const GetSearchUserGroupOrg = (
  searchKey: string,
  options?: {
    members?: boolean;
    orgs?: boolean;
    groups?: boolean;
  }
) =>
  GET<SearchResult>('/api/support/user/search', { searchKey, ...options }, { maxQuantity: 1 });

export const ExportMembers = () => GET<{ csv: string }>('/api/support/user/team/member/export');
