import { GET, POST } from '@/web/common/api/request';
import type { BillTypeEnum } from '@/packages/global/support/wallet/bill/constants';
import type { InvoiceFileInfo } from '@/packages/global/support/wallet/bill/invoice/type';
import type { InvoiceType } from '@/packages/global/support/wallet/bill/type';
import type { InvoiceSchemaType } from '@/packages/global/support/wallet/bill/type';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';

export type invoiceBillDataType = {
  type: BillTypeEnum;
  price: number;
  createTime: Date;
  _id: string;
};

export const getInvoiceBillsList = () =>
  GET<invoiceBillDataType[]>(`/api/support/wallet/bill/invoice/unInvoiceList`);

export const submitInvoice = (data: InvoiceType) =>
  POST(`/api/support/wallet/bill/invoice/submit`, data);

export const getInvoiceRecords = (data: PaginationProps) =>
  POST<PaginationResponse<InvoiceSchemaType>>(`/api/support/wallet/bill/invoice/records`, data);
