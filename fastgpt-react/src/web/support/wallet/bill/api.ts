import { GET, POST, PUT } from '@/web/common/api/request';
import type {
  CheckPayResultResponse,
  CreateBillProps,
  CreateBillResponse,
  CreateOrderResponse,
  UpdatePaymentProps
} from '@/packages/global/support/wallet/bill/api';
import type { BillTypeEnum } from '@/packages/global/support/wallet/bill/constants';
import { BillStatusEnum } from '@/packages/global/support/wallet/bill/constants';
import type { BillSchemaType } from '@/packages/global/support/wallet/bill/type.d';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';

export const getBills = (
  data: PaginationProps<{
    type?: BillTypeEnum;
  }>
) => POST<PaginationResponse<BillSchemaType>>(`/api/support/wallet/bill/list`, data);

export const postCreatePayBill = (data: CreateBillProps) =>
  POST<CreateBillResponse>(`/api/support/wallet/bill/create`, data);

export const checkBalancePayResult = (payId: string) =>
  GET<CheckPayResultResponse>(`/api/support/wallet/bill/pay/checkPayResult`, { payId }).then(
    (data) => {
      try {
        if (data.status === BillStatusEnum.SUCCESS) {
          GET('/api/common/system/unlockTask');
        }
      } catch (error) {}
      return data;
    }
  );

export const putUpdatePayment = (data: UpdatePaymentProps) =>
  PUT<CreateOrderResponse>(`/api/support/wallet/bill/pay/updatePayment`, data);

export const balanceConversion = () => GET<string>(`/api/support/wallet/bill/balanceConversion`);
