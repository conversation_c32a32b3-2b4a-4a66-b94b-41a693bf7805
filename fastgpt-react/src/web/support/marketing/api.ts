import { POST } from '@/web/common/api/request';
// import {
//   type FetchWorkflowQuery,
//   type FetchWorkflowResponseType
// } from '@/pages/api/support/marketing/fetchWorkflow';
type FetchWorkflowQuery = {};
type FetchWorkflowResponseType = {
  data: Record<string, any>;
};

export const postFetchWorkflow = (data: FetchWorkflowQuery) =>
  POST<FetchWorkflowResponseType>('/api/support/marketing/fetchWorkflow', data);
