import { GET, PUT, DELETE, POST } from '@/web/common/api/request';
import type { ModelTypeEnum } from '@/packages/global/core/ai/model';
import type { ModelProviderIdType } from '@/packages/global/core/ai/provider';
// import type { listResponse } from '@/pages/api/core/ai/model/list';
type listResponse = {
    type: `${ModelTypeEnum}`;
    name: string;
    avatar: string | undefined;
    provider: ModelProviderIdType;
    model: string;
    charsPointsPrice?: number;
    inputPrice?: number;
    outputPrice?: number;

    isActive: boolean;
    isCustom: boolean;

    // Tag
    contextToken?: number;
    vision?: boolean;
    toolChoice?: boolean;
}[];
// import type { updateBody } from '@/pages/api/core/ai/model/update';
type updateBody = {
    model: string;
    metadata?: Record<string, any>;
};
// import type { deleteQuery } from '@/pages/api/core/ai/model/delete';
type deleteQuery = {
    model: string;
};
import type { SystemModelItemType } from '@/packages/service/core/ai/type';
// import type { updateWithJsonBody } from '@/pages/api/core/ai/model/updateWithJson';
type updateWithJsonBody = {
    config: string;
};
// import type { updateDefaultBody } from '@/pages/api/core/ai/model/updateDefault';
type updateDefaultBody = {
    [ModelTypeEnum.llm]?: string;
    [ModelTypeEnum.embedding]?: string;
    [ModelTypeEnum.tts]?: string;
    [ModelTypeEnum.stt]?: string;
    [ModelTypeEnum.rerank]?: string;
    datasetTextLLM?: string;
    datasetImageLLM?: string;
};
// import type { testQuery } from '@/pages/api/core/ai/model/test';
type testQuery = { model: string; channelId?: number };

export const getSystemModelList = () => GET<listResponse>('/api/core/ai/model/list');
export const getSystemModelDetail = (model: string) =>
  GET<SystemModelItemType>('/api/core/ai/model/detail', { model });

export const getSystemModelDefaultConfig = (model: string) =>
  GET<SystemModelItemType>('/api/core/ai/model/getDefaultConfig', { model });

export const putSystemModel = (data: updateBody) => PUT('/api/core/ai/model/update', data);

export const deleteSystemModel = (data: deleteQuery) => POST('/api/core/ai/model/delete', data);

export const getModelConfigJson = () => GET<string>('/api/core/ai/model/getConfigJson');
export const putUpdateWithJson = (data: updateWithJsonBody) =>
  PUT('/api/core/ai/model/updateWithJson', data);

export const getTestModel = (data: testQuery) => GET('/api/core/ai/model/test', data);

export const putUpdateDefaultModels = (data: updateDefaultBody) =>
  PUT('/api/core/ai/model/updateDefault', data);
