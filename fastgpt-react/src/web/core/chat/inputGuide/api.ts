import { GET, POST, DELETE, PUT } from '@/web/common/api/request';
// import type {
//   ChatInputGuideProps,
//   ChatInputGuideResponse
// } from '@/pages/api/core/chat/inputGuide/list';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';
import { type ChatInputGuideSchemaType } from '@/packages/global/core/chat/inputGuide/type';
type ChatInputGuideProps = PaginationProps<{
  appId: string;
  searchKey: string;
}>;
type ChatInputGuideResponse = PaginationResponse<ChatInputGuideSchemaType>;
// import type {
//   countChatInputGuideTotalQuery,
//   countChatInputGuideTotalResponse
// } from '@/pages/api/core/chat/inputGuide/countTotal';
type countChatInputGuideTotalQuery = { appId: string };
type countChatInputGuideTotalResponse = { total: number };
// import type {
//   createInputGuideBody,
//   createInputGuideResponse
// } from '@/pages/api/core/chat/inputGuide/create';
type createInputGuideBody = {
  appId: string;
  textList: string[];
};
type createInputGuideResponse = {
  insertLength: number;
};
import type { updateInputGuideBody } from '@/pages/api/core/chat/inputGuide/update';
import type { deleteInputGuideBody } from '@/pages/api/core/chat/inputGuide/delete';
// import type {
//   QueryChatInputGuideBody,
//   QueryChatInputGuideResponse
// } from '@/pages/api/core/chat/inputGuide/query';
import { type OutLinkChatAuthProps } from '@/packages/global/support/permission/chat';
type QueryChatInputGuideBody = OutLinkChatAuthProps & {
  appId: string;
  searchKey: string;
};
type QueryChatInputGuideResponse = string[];

import type { deleteAllInputGuideBody } from '@/pages/api/core/chat/inputGuide/deleteAll';

export const getCountChatInputGuideTotal = (data: countChatInputGuideTotalQuery) =>
  GET<countChatInputGuideTotalResponse>(`/api/core/chat/inputGuide/countTotal`, data);
/**
 * Get chat input guide list
 */
export const getChatInputGuideList = (data: ChatInputGuideProps) =>
  POST<ChatInputGuideResponse>(`/api/core/chat/inputGuide/list`, data);

export const queryChatInputGuideList = (data: QueryChatInputGuideBody, url?: string) => {
  if (url) {
    return GET<QueryChatInputGuideResponse>(url, data, {
      withCredentials: !url
    });
  }
  return POST<QueryChatInputGuideResponse>(`/api/core/chat/inputGuide/query`, data);
};

export const postChatInputGuides = (data: createInputGuideBody) =>
  POST<createInputGuideResponse>(`/api/core/chat/inputGuide/create`, data);
export const putChatInputGuide = (data: updateInputGuideBody) =>
  PUT(`/api/core/chat/inputGuide/update`, data);
export const delChatInputGuide = (data: deleteInputGuideBody) =>
  POST(`/api/core/chat/inputGuide/delete`, data);
export const delAllChatInputGuide = (data: deleteAllInputGuideBody) =>
  POST(`/api/core/chat/inputGuide/deleteAll`, data);
