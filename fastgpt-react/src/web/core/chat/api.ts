import { GET, POST, DELETE, PUT } from '@/web/common/api/request';
import type { ChatHistoryItemType, ChatHistoryItemResType } from '@/packages/global/core/chat/type.d';
import type { getResDataQuery } from '@/pages/api/core/chat/getResData';
import type {
  CloseCustomFeedbackParams,
  InitChatProps,
  InitChatResponse,
  InitOutLinkChatProps,
  GetHistoriesProps,
  InitTeamChatProps
} from '@/global/core/chat/api.d';

import type {
  AdminUpdateFeedbackParams,
  ClearHistoriesProps,
  DelHistoryProps,
  DeleteChatItemProps,
  UpdateHistoryProps
} from '@/global/core/chat/api.d';
import type { UpdateChatFeedbackProps } from '@/packages/global/core/chat/api';
import type { AuthTeamTagTokenProps } from '@/packages/global/support/user/team/tag';
import type { AppListItemType } from '@/packages/global/core/app/type';
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';
// import type {
//   getPaginationRecordsBody,
//   getPaginationRecordsResponse
// } from '@/pages/api/core/chat/getPaginationRecords';
import { type GetChatRecordsProps } from '@/global/core/chat/api';
import { type ChatItemType } from '@/packages/global/core/chat/type';
type getPaginationRecordsBody = PaginationProps & GetChatRecordsProps;
type getPaginationRecordsResponse = PaginationResponse<ChatItemType>;
import type { GetQuoteProps, GetQuotesRes } from '@/pages/api/core/chat/quote/getQuote';
// import type {
//   GetCollectionQuoteProps,
//   GetCollectionQuoteRes
// } from '@/pages/api/core/chat/quote/getCollectionQuote';
import {
  type LinkedListResponse,
  type LinkedPaginationProps
} from '@/packages/common/fetch/type';
import {
  type DatasetCiteItemType,
} from '@/packages/global/core/dataset/type';
type GetCollectionQuoteProps = LinkedPaginationProps & {
  chatId: string;
  chatItemDataId: string;

  collectionId: string;

  appId: string;
  shareId?: string;
  outLinkUid?: string;
  teamId?: string;
  teamToken?: string;
};
type GetCollectionQuoteRes = LinkedListResponse<DatasetCiteItemType>;

/**
 * 获取初始化聊天内容
 */
export const getInitChatInfo = (data: InitChatProps) =>
  GET<InitChatResponse>(`/api/core/chat/init`, data);
export const getInitOutLinkChatInfo = (data: InitOutLinkChatProps) =>
  GET<InitChatResponse>(`/api/core/chat/outLink/init`, data);
export const getTeamChatInfo = (data: InitTeamChatProps) =>
  GET<InitChatResponse>(`/api/core/chat/team/init`, data);

/**
 * get current window history(appid or shareId)
 */
export const getChatHistories = (data: PaginationProps<GetHistoriesProps>) =>
  POST<PaginationResponse<ChatHistoryItemType>>('/api/core/chat/getHistories', data);
/**
 * get detail responseData by dataId appId chatId
 */
export const getChatResData = (data: getResDataQuery) =>
  GET<ChatHistoryItemResType[]>(`/api/core/chat/getResData`, data);

export const getChatRecords = (data: getPaginationRecordsBody) =>
  POST<getPaginationRecordsResponse>('/api/core/chat/getPaginationRecords', data);

/**
 * delete one history
 */
export const delChatHistoryById = (data: DelHistoryProps) => DELETE(`/api/core/chat/delHistory`, data);
/**
 * clear all history by appid
 */
export const delClearChatHistories = (data: ClearHistoriesProps) =>
  DELETE(`/api/core/chat/clearHistories`, data);

/**
 * delete one chat record
 */
export const delChatRecordById = (data: DeleteChatItemProps) =>
  DELETE(`/api/core/chat/item/delete`, data);

/**
 * 修改历史记录: 标题/置顶
 */
export const putChatHistory = (data: UpdateHistoryProps) => PUT('/api/core/chat/updateHistory', data);

/* -------------- feedback ------------ */
export const updateChatUserFeedback = (data: UpdateChatFeedbackProps) =>
  POST('/api/core/chat/feedback/updateUserFeedback', data);

export const updateChatAdminFeedback = (data: AdminUpdateFeedbackParams) =>
  POST('/api/core/chat/feedback/adminUpdate', data);

export const closeCustomFeedback = (data: CloseCustomFeedbackParams) =>
  POST('/api/core/chat/feedback/closeCustom', data).catch();

/* team chat */
/**
 * Get the app that can be used with this token
 */
export const getMyTokensApps = (data: AuthTeamTagTokenProps) =>
  GET<AppListItemType[]>(`/api/support/user/team/tag/getAppsByTeamTokens`, data);

/**
 * 获取团队分享的对话列表 initTeamChat
 * @param data
 * @returns
 */
export const getinitTeamChat = (data: { teamId: string; authToken: string; appId: string }) =>
  GET(`/api/core/chat/initTeamChat`, data);

export const getQuoteDataList = (data: GetQuoteProps) =>
  POST<GetQuotesRes>(`/api/core/chat/quote/getQuote`, data);

export const getCollectionQuote = (data: GetCollectionQuoteProps) =>
  POST<GetCollectionQuoteRes>(`/api/core/chat/quote/getCollectionQuote`, data);
