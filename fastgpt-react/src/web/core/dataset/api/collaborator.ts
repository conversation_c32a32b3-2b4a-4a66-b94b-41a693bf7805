import type {
  UpdateDatasetCollaboratorBody,
  DatasetCollaboratorDeleteParams
} from '@/packages/global/core/dataset/collaborator';
import { DELETE, GET, POST } from '@/web/common/api/request';
import type { CollaboratorItemType } from '@/packages/global/support/permission/collaborator';

export const getCollaboratorList = (datasetId: string) =>
  GET<CollaboratorItemType[]>('/api/core/dataset/collaborator/list', { datasetId });

export const postUpdateDatasetCollaborators = (body: UpdateDatasetCollaboratorBody) =>
  POST('/api/core/dataset/collaborator/update', body);

export const deleteDatasetCollaborators = (params: DatasetCollaboratorDeleteParams) =>
  DELETE('/api/core/dataset/collaborator/delete', params);
