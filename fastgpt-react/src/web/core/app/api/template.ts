import type { ListParams } from '@/pages/api/core/app/template/list';
import { GET } from '@/web/common/api/request';
import { useSystemStore } from '@/web/common/system/useSystemStore';
import type { AppTemplateSchemaType, TemplateTypeSchemaType } from '@/packages/global/core/app/type';
import { defaultTemplateTypes } from '@/packages/core/workflow/constants';

export const getTemplateMarketItemList = (data: ListParams) =>
  GET<AppTemplateSchemaType[]>(`/api/core/app/template/list`, data);

export const getTemplateMarketItemDetail = (templateId: string) =>
  GET<AppTemplateSchemaType>(`/api/core/app/template/detail?templateId=${templateId}`);

export const getTemplateTagList = () => {
  return useSystemStore.getState()?.feConfigs?.isPlus
    ? GET<TemplateTypeSchemaType[]>('/api/core/app/template/getTemplateTypes')
    : Promise.resolve(defaultTemplateTypes);
};
