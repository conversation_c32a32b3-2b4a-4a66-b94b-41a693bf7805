import type { PostPublishAppProps } from '@/global/core/app/api';
import { GET, POST } from '@/web/common/api/request';
import type { AppVersionSchemaType } from '@/packages/global/core/app/version';
import type { PaginationProps } from '@/packages/common/fetch/type';
// import type {
//   getLatestVersionQuery,
//   getLatestVersionResponse
// } from '@/pages/api/core/app/version/latest';
import { type AppChatConfigType } from '@/packages/global/core/app/type';
import { type StoreEdgeItemType } from '@/packages/global/core/workflow/type/edge';
import { type StoreNodeItemType } from '@/packages/global/core/workflow/type/node';
type getLatestVersionQuery = {
  appId: string;
};
type getLatestVersionResponse = {
  nodes: StoreNodeItemType[];
  edges: StoreEdgeItemType[];
  chatConfig: AppChatConfigType;
};
import type { UpdateAppVersionBody } from '@/pages/api/core/app/version/update';
import type { versionListResponse } from '@/packages/global/core/app/version';

export const getAppLatestVersion = (data: getLatestVersionQuery) =>
  GET<getLatestVersionResponse>('/api/core/app/version/latest', data);

export const postPublishApp = (appId: string, data: PostPublishAppProps) =>
  POST(`/api/core/app/version/publish?appId=${appId}`, data);

export const getAppVersionList = (data: PaginationProps<{ appId: string }>) =>
  POST<versionListResponse>('/api/core/app/version/list', data);

export const getAppVersionDetail = (versionId: string, appId: string) =>
  GET<AppVersionSchemaType>(`/api/core/app/version/detail?versionId=${versionId}&appId=${appId}`);

export const updateAppVersion = (data: UpdateAppVersionBody) =>
  POST(`/api/core/app/version/update`, data);
