import { GET, POST, DELETE, PUT } from '@/web/common/api/request';
import type { AppDetailType, AppListItemType } from '@/packages/global/core/app/type.d';
import type { GetAppChatLogsParams } from '@/global/core/api/appReq.d';
import type { AppUpdateParams, AppChangeOwnerBody } from '@/global/core/app/api';
import type { CreateAppBody } from '@/pages/api/core/app/create';
import type { ListAppBody } from '@/pages/api/core/app/list';
import type { AppLogsListItemType } from '@/types/app';
import type { PaginationResponse } from '@/packages/common/fetch/type';
import type { getBasicInfoResponse } from '@/pages/api/core/app/getBasicInfo';

/**
 * 获取应用列表
 */
export const getMyApps = (data?: ListAppBody) =>
  POST<AppListItemType[]>('/api/core/app/list', data, {
    maxQuantity: 1
  });

/**
 * 创建一个应用
 */
export const postCreateApp = (data: CreateAppBody) => POST<string>('/api/core/app/create', data);

export const getMyAppsByTags = (data: {}) => POST(`/api/core/chat/team/getApps`, data);
/**
 * 根据 ID 删除应用
 */
export const delAppById = (id: string) => DELETE(`/api/core/app/del?appId=${id}`);

/**
 * 根据 ID 获取应用
 */
export const getAppDetailById = (id: string) => GET<AppDetailType>(`/api/core/app/detail?appId=${id}`);

/**
 * 根据 ID 更新应用
 */
export const putAppById = (id: string, data: AppUpdateParams) =>
  PUT(`/api/core/app/update?appId=${id}`, data);

/**
 * Get app basic info by ids
 */
export const getAppBasicInfoByIds = (ids: string[]) =>
  POST<getBasicInfoResponse>(`/api/core/app/getBasicInfo`, { ids });

// =================== chat logs
export const getAppChatLogs = (data: GetAppChatLogsParams) =>
  POST<PaginationResponse<AppLogsListItemType>>(`/api/core/app/getChatLogs`, data, { maxQuantity: 1 });

export const resumeInheritPer = (appId: string) =>
  GET(`/api/core/app/resumeInheritPermission`, { appId });

export const changeOwner = (data: AppChangeOwnerBody) => POST(`/api/core/app/changeOwner`, data);
