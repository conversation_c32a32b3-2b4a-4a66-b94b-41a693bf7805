import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {useNavigate, useLocation , useSearchParams} from "react-router-dom";
import { Box, Flex, Drawer, DrawerOverlay, DrawerContent } from '@chakra-ui/react';
import { streamFetch } from '@/web/common/api/fetch';
import SideBar from '@/components/SideBar';
import { GPTMessages2Chats } from '@/packages/global/core/chat/adapt';

import ChatBox from '@/components/core/chat/ChatContainer/ChatBox';
import type { StartChatFnProps } from '@/components/core/chat/ChatContainer/type';

import PageContainer from '@/components/PageContainer';
import ChatHeader from '@/pageComponents/chat/ChatHeader';
import ChatHistorySlider from '@/pageComponents/chat/ChatHistorySlider';
import { useTranslation } from 'react-i18next';
import { getInitOutLinkChatInfo } from '@/web/core/chat/api';
import { getChatTitleFromChatMessage } from '@/packages/global/core/chat/utils';
// import { MongoOutLink } from '@fastgpt/service/support/outLink/schema';
// import { addLog } from '@fastgpt/service/common/system/log';

import NextHead from '@/components/common/NextHead';
import { useContextSelector } from 'use-context-selector';
import ChatContextProvider, { ChatContext } from '@/web/core/chat/context/chatContext';
import { GetChatTypeEnum } from '@/global/core/chat/constants';
import { useMount } from 'ahooks';
import { useRequest2 } from '@/packages/hooks/useRequest';
import { getNanoid } from '@/packages/global/common/string/tools';

import { lazy } from 'react';
import { useSystem } from '@/packages/hooks/useSystem';
import { useShareChatStore } from '@/web/core/chat/storeShareChat';
import ChatItemContextProvider, { ChatItemContext } from '@/web/core/chat/context/chatItemContext';
import ChatRecordContextProvider, {
  ChatRecordContext
} from '@/web/core/chat/context/chatRecordContext';
import { useChatStore } from '@/web/core/chat/context/useChatStore';
import { ChatSourceEnum } from '@/packages/global/core/chat/constants';
import { useI18nLng } from '@/packages/hooks/useI18n';
import { type AppSchema } from '@/packages/global/core/app/type';
import ChatQuoteList from '@/pageComponents/chat/ChatQuoteList';
import { useToast } from '@/packages/hooks/useToast';
import { GET } from '@/web/common/api/request';

const CustomPluginRunBox = lazy(() => import('@/pageComponents/chat/CustomPluginRunBox'));

type Props = {
  shareId: string;
  authToken: string;
  customUid: string;
};

const OutLink = (props: Props & {
  appId?: string;
  appName?: string;
  appAvatar?: string;
  appIntro?: string;
  showRawSource?: boolean;
  responseDetail?: boolean;
  showNodeStatus?: boolean;
}) => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const {
    shareId = '',
    showHistory = '1',
    showHead = '1',
    authToken,
    customUid,
    ...customVariables
  } = {
    shareId: searchParams.get('shareId') || '',
    showHistory: searchParams.get('showHistory') || '1',
    showHead: searchParams.get('showHead') || '1',
    authToken: searchParams.get('authToken') || '',
    customUid: searchParams.get('customUid') || '',
    // 其他自定义变量
    ...Object.fromEntries(
      Array.from(searchParams.entries()).filter(([key]) => 
        !['shareId', 'showHistory', 'showHead', 'authToken', 'customUid'].includes(key)
      )
    )
  };
  const { isPc } = useSystem();
  const { outLinkAuthData, appId, chatId } = useChatStore();

  const isOpenSlider = useContextSelector(ChatContext, (v) => v.isOpenSlider);
  const onCloseSlider = useContextSelector(ChatContext, (v) => v.onCloseSlider);
  const forbidLoadChat = useContextSelector(ChatContext, (v) => v.forbidLoadChat);
  const onChangeChatId = useContextSelector(ChatContext, (v) => v.onChangeChatId);
  const onUpdateHistoryTitle = useContextSelector(ChatContext, (v) => v.onUpdateHistoryTitle);

  const resetVariables = useContextSelector(ChatItemContext, (v) => v.resetVariables);
  const isPlugin = useContextSelector(ChatItemContext, (v) => v.isPlugin);
  const setChatBoxData = useContextSelector(ChatItemContext, (v) => v.setChatBoxData);
  const datasetCiteData = useContextSelector(ChatItemContext, (v) => v.datasetCiteData);
  const setCiteModalData = useContextSelector(ChatItemContext, (v) => v.setCiteModalData);
  const isResponseDetail = useContextSelector(ChatItemContext, (v) => v.isResponseDetail);

  const chatRecords = useContextSelector(ChatRecordContext, (v) => v.chatRecords);
  const totalRecordsCount = useContextSelector(ChatRecordContext, (v) => v.totalRecordsCount);
  const isChatRecordsLoaded = useContextSelector(ChatRecordContext, (v) => v.isChatRecordsLoaded);

  const initSign = useRef(false);
  const { data, loading } = useRequest2(
    async () => {
      const shareId = outLinkAuthData.shareId;
      const outLinkUid = outLinkAuthData.outLinkUid;
      if (!outLinkUid || !shareId || forbidLoadChat.current) return;

      const res = await getInitOutLinkChatInfo({
        chatId,
        shareId,
        outLinkUid
      });

      setChatBoxData(res);

      resetVariables({
        variables: res.variables,
        variableList: res.app?.chatConfig?.variables
      });

      return res;
    },
    {
      manual: false,
      refreshDeps: [shareId, outLinkAuthData, chatId],
      onFinally() {
        forbidLoadChat.current = false;
      }
    }
  );
  useEffect(() => {
    if (initSign.current === false && data && isChatRecordsLoaded) {
      initSign.current = true;
      if (window !== top) {
        window.top?.postMessage({ type: 'shareChatReady' }, '*');
      }
    }
  }, [data, isChatRecordsLoaded]);

  const startChat = useCallback(
    async ({
      messages,
      controller,
      generatingMessage,
      variables,
      responseChatItemId
    }: StartChatFnProps) => {
      const completionChatId = chatId || getNanoid();
      const histories = messages.slice(-1);

      //post message to report chat start
      window.top?.postMessage(
        {
          type: 'shareChatStart',
          data: {
            question: histories[0]?.content
          }
        },
        '*'
      );

      const { responseText } = await streamFetch({
        url: '/share/v2/chat/completions',
        data: {
          messages: histories,
          variables: {
            ...variables,
            ...customVariables
          },
          responseChatItemId,
          chatId: completionChatId,
          ...outLinkAuthData,
          retainDatasetCite: isResponseDetail
        },
        onMessage: generatingMessage,
        abortCtrl: controller
      });

      const newTitle = getChatTitleFromChatMessage(GPTMessages2Chats(histories)[0]);

      // new chat
      if (completionChatId !== chatId) {
        onChangeChatId(completionChatId, true);
      }
      onUpdateHistoryTitle({ chatId: completionChatId, newTitle });

      // update chat window
      setChatBoxData((state) => ({
        ...state,
        title: newTitle
      }));

      // hook message
      window.top?.postMessage(
        {
          type: 'shareChatFinish',
          data: {
            question: histories[0]?.content,
            answer: responseText
          }
        },
        '*'
      );

      return { responseText, isNewChat: forbidLoadChat.current };
    },
    [
      chatId,
      customVariables,
      outLinkAuthData,
      isResponseDetail,
      onUpdateHistoryTitle,
      setChatBoxData,
      forbidLoadChat,
      onChangeChatId
    ]
  );

  // window init
  const [isEmbed, setIdEmbed] = useState(true);
  useMount(() => {
    setIdEmbed(window !== top);
  });

  const RenderHistoryList = useMemo(() => {
    const Children = (
      <ChatHistorySlider
        confirmClearText={t('common:core.chat.Confirm to clear share chat history')}
      />
    );

    if (showHistory !== '1') return null;

    return isPc ? (
      <SideBar externalTrigger={!!datasetCiteData}>{Children}</SideBar>
    ) : (
      <Drawer
        isOpen={isOpenSlider}
        placement="left"
        autoFocus={false}
        size={'xs'}
        onClose={onCloseSlider}
      >
        <DrawerOverlay backgroundColor={'rgba(255,255,255,0.5)'} />
        <DrawerContent maxWidth={'75vw'} boxShadow={'2px 0 10px rgba(0,0,0,0.15)'}>
          {Children}
        </DrawerContent>
      </Drawer>
    );
  }, [isOpenSlider, isPc, onCloseSlider, datasetCiteData, showHistory, t]);

  return (
    <>
      <NextHead
        title={props.appName || data?.app?.name || 'AI'}
        desc={props.appIntro || data?.app?.intro || 'AI'}
        icon={props.appAvatar || data?.app?.avatar || ''}
      />
      <Flex
        h={'full'}
        gap={4}
        {...(isEmbed ? { p: '0 !important', borderRadius: '0', boxShadow: 'none' } : { p: [0, 5] })}
      >
        {(!datasetCiteData || isPc) && (
          <PageContainer flex={'1 0 0'} w={0} p={'0 !important'}>
            <Flex h={'100%'} flexDirection={['column', 'row']}>
              {RenderHistoryList}

              {/* chat container */}
              <Flex
                position={'relative'}
                h={[0, '100%']}
                w={['100%', 0]}
                flex={'1 0 0'}
                flexDirection={'column'}
              >
                {/* header */}
                {showHead === '1' ? (
                  <ChatHeader
                    history={chatRecords}
                    totalRecordsCount={totalRecordsCount}
                    showHistory={showHistory === '1'}
                  />
                ) : null}
                {/* chat box */}
                <Box flex={1} bg={'white'}>
                  {isPlugin ? (
                    <CustomPluginRunBox
                      appId={appId}
                      chatId={chatId}
                      outLinkAuthData={outLinkAuthData}
                      onNewChat={() => onChangeChatId(getNanoid())}
                      onStartChat={startChat}
                    />
                  ) : (
                    <ChatBox
                      isReady={!loading}
                      appId={appId}
                      chatId={chatId}
                      outLinkAuthData={outLinkAuthData}
                      feedbackType={'user'}
                      onStartChat={startChat}
                      chatType="share"
                    />
                  )}
                </Box>
              </Flex>
            </Flex>
          </PageContainer>
        )}

        {datasetCiteData && (
          <PageContainer flex={'1 0 0'} w={0} maxW={'560px'} p={'0 !important'}>
            <ChatQuoteList
              rawSearch={datasetCiteData.rawSearch}
              metadata={datasetCiteData.metadata}
              onClose={() => setCiteModalData(undefined)}
            />
          </PageContainer>
        )}
      </Flex>
    </>
  );
};

const Render = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  
  // 从 URL 参数中获取参数
  const shareId = searchParams.get('shareId') || '';
  const authToken = searchParams.get('authToken') || '';
  const customUid = searchParams.get('customUid') || '';
  
  const { localUId, setLocalUId, loaded } = useShareChatStore();
  const { source, chatId, setSource, setAppId, setOutLinkAuthData } = useChatStore();
  const { setUserDefaultLng } = useI18nLng();
  
  // 客户端获取应用信息
  const [appData, setAppData] = useState<{
    appId: string;
    associatedApp: { name: string; avatar: string; intro: string; };
    showRawSource: boolean;
    responseDetail: boolean;
    showNodeStatus: boolean;
  }>({
    appId: '',
    associatedApp: { name: 'AI', avatar: '', intro: 'AI' },
    showRawSource: false,
    responseDetail: false,
    showNodeStatus: false
  });
  const [appLoading, setAppLoading] = useState(true); // 初始化为true，表示正在加载
  const fetchedShareIds = useRef(new Set<string>());
  
  useEffect(() => {
    if (!shareId || fetchedShareIds.current.has(shareId)) {
      setAppLoading(false); // 如果没有shareId，设置为不加载
      return;
    }
    
    fetchedShareIds.current.add(shareId);
    
    const fetchAppData = async () => {
      try {
        setAppLoading(true);
        const data = await GET('/api/support/outLink/app', { shareId });
        if (data) {
          setAppData(data);
        }
      } catch (error) {
        console.error('❌ 获取外链应用信息失败:', error);
        toast({
          status: 'warning',
          title: t('chat:invalid_share_url')
        });
      } finally {
        setAppLoading(false);
      }
    };
    
    fetchAppData();
  }, [shareId, toast, t]); // 添加必要的依赖
  
  const appId = appData.appId || '';
  const appName = appData.associatedApp?.name || 'AI';
  const appAvatar = appData.associatedApp?.avatar || '';
  const appIntro = appData.associatedApp?.intro || 'AI';

  const showRawSource = appData.showRawSource ?? false;
  const responseDetail = appData.responseDetail ?? false;
  const showNodeStatus = appData.showNodeStatus ?? false;

  useMount(() => {
    setSource('share');
    setUserDefaultLng(true);
  });

  // Set default localUId
  useEffect(() => {
    if (loaded) {
      if (!localUId) {
        setLocalUId(`shareChat-${Date.now()}-${getNanoid(24)}`);
      }
    }
  }, [loaded, localUId, setLocalUId]);

  // Init outLinkAuthData
  useEffect(() => {
    const outLinkUid = authToken || customUid || localUId || '';
    if (outLinkUid) {
      setOutLinkAuthData({
        shareId,
        outLinkUid
      });
    }
    return () => {
      setOutLinkAuthData({});
    };
  }, [authToken, customUid, localUId, setOutLinkAuthData, shareId]);

  // Watch appId - 只在appId有值时才设置
  useEffect(() => {
    if (appId) {
      setAppId(appId);
    }
  }, [appId, setAppId]);

  // 如果还在加载应用数据，显示加载状态
  if (appLoading) {
    return <NextHead title="Loading..." desc="Loading..." icon="" />;
  }

  // 如果appId还没有加载完成，也不渲染，确保appId已经设置到全局状态
  if (!appId) {
    return <NextHead title="Loading..." desc="Loading..." icon="" />;
  }

  const chatHistoryProviderParams = { shareId, outLinkUid: authToken || customUid || localUId || '' };
  const chatRecordProviderParams = {
    appId,
    shareId,
    outLinkUid: chatHistoryProviderParams.outLinkUid,
    chatId,
    type: GetChatTypeEnum.outLink
  };

  // 构建props对象
  const props = {
    shareId,
    authToken,
    customUid
  };

  return source === ChatSourceEnum.share ? (
    <ChatContextProvider params={chatHistoryProviderParams}>
      <ChatItemContextProvider
        showRouteToAppDetail={false}
        showRouteToDatasetDetail={false}
        isShowReadRawSource={showRawSource}
        isResponseDetail={responseDetail}
        showNodeStatus={showNodeStatus}
      >
        <ChatRecordContextProvider params={chatRecordProviderParams}>
          <OutLink {...props} appId={appId} appName={appName} appAvatar={appAvatar} appIntro={appIntro} showRawSource={showRawSource} responseDetail={responseDetail} showNodeStatus={showNodeStatus} />
        </ChatRecordContextProvider>
      </ChatItemContextProvider>
    </ChatContextProvider>
  ) : (
    <NextHead title={appName} desc={appIntro} icon={appAvatar} />
  );
};

export default React.memo(Render);
