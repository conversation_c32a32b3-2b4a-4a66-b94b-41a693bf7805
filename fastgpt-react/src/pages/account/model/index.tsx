'use client';
import React, { useMemo, useState } from 'react';
import AccountContainer from '@/pageComponents/account/AccountContainer';
import { Box, Flex } from '@chakra-ui/react';
import ModelTable from '@/components/core/ai/ModelTable';
import { useUserStore } from '@/web/support/user/useUserStore';
import FillRowTabs from '@/packages/components/common/Tabs/FillRowTabs';
import { useTranslation } from 'react-i18next';
import { lazy } from 'react';
import { useSystemStore } from '@/web/common/system/useSystemStore';

const ModelConfigTable = lazy(() => import('@/pageComponents/account/model/ModelConfigTable'));
const ChannelTable = lazy(() => import('@/pageComponents/account/model/Channel'));
const ChannelLog = lazy(() => import('@/pageComponents/account/model/Log'));
const ModelDashboard = lazy(() => import('@/pageComponents/account/model/ModelDashboard'));

type TabType = 'model' | 'config' | 'channel' | 'channel_log' | 'account_model';

const ModelProvider = () => {
  const { t } = useTranslation();
  const { feConfigs } = useSystemStore();

  const [tab, setTab] = useState<TabType>('model');

  const Tab = useMemo(() => {
    return (
      <FillRowTabs<TabType>
        list={[
          { label: t('account:active_model'), value: 'model' },
          { label: t('account:config_model'), value: 'config' },
          { label: t('account_model:log'), value: 'channel_log' },
        ]}
        value={tab}
        py={1}
        onChange={setTab}
      />
    );
  }, [feConfigs.show_aiproxy, t, tab]);

  return (
    <AccountContainer>
      <Flex h={'100%'} flexDirection={'column'} gap={4} py={4} px={6}>
        {tab === 'model' && <ValidModelTable Tab={Tab} />}
        {tab === 'config' && <ModelConfigTable Tab={Tab} />}
        {tab === 'channel' && <ChannelTable Tab={Tab} />}
        {tab === 'channel_log' && <ChannelLog Tab={Tab} />}
        {tab === 'account_model' && <ModelDashboard Tab={Tab} />}
      </Flex>
    </AccountContainer>
  );
};


const ValidModelTable = ({ Tab }: { Tab: React.ReactNode }) => {
  const { userInfo } = useUserStore();
  const isRoot = true; //userInfo?.username === 'root';
  return (
    <>
      {isRoot && <Flex justifyContent={'space-between'}>{Tab}</Flex>}
      <Box flex={'1 0 0'}>
        <ModelTable />
      </Box>
    </>
  );
};

export default ModelProvider;
