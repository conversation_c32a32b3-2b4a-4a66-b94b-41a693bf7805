import React, { type ForwardedRef, forwardRef } from 'react';
import { Image, type ImageProps } from '@chakra-ui/react';
import { getStaticUrl } from '../../../common/system/utils';

/**
 * 支持的图片MIME类型
 */
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
] as const;

/**
 * 检测字符串是否为base64编码
 * @param str 要检测的字符串
 * @returns 是否为base64格式
 */
const isBase64 = (str?: string): boolean => {
  if (!str) return false;
  
  // 检查是否为data URL格式的base64
  if (str.startsWith('data:')) {
    return str.includes('base64,') && SUPPORTED_IMAGE_TYPES.some(type => str.startsWith(`data:${type}`));
  }
  
  // 检查是否为纯base64字符串（仅包含base64字符）
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  return base64Regex.test(str) && str.length % 4 === 0 && str.length > 10;
};

/**
 * 处理图片源地址
 * @param src 原始图片源
 * @returns 处理后的图片源
 */
const processImageSrc = (src?: string): string | undefined => {
  if (!src) return src;
  
  try {
    // 如果是base64格式，直接返回
    if (isBase64(src)) {
      // 如果是纯base64字符串，添加data URL前缀（默认为PNG格式）
      if (!src.startsWith('data:')) {
        return `data:image/png;base64,${src}`;
      }
      return src;
    }
    
    // 如果是普通URL，使用getWebReqUrl处理
    return getStaticUrl(src);
  } catch (error) {
    console.warn('处理图片源地址时出错:', error);
    return src; // 发生错误时返回原始src
  }
};

/**
 * 扩展的ImageProps，添加base64支持说明
 */
export interface MyImageProps extends ImageProps {
  /**
   * 图片源，支持以下格式：
   * - 普通URL: '/path/to/image.jpg'
   * - 完整URL: 'https://example.com/image.jpg'  
   * - Data URL: 'data:image/png;base64,iVBORw0...'
   * - 纯Base64: 'iVBORw0KGgoAAAANSUhEUgAA...'
   */
  src?: string;
}

const MyImage = (props: MyImageProps, ref?: ForwardedRef<any>) => {
  const processedSrc = processImageSrc(props.src);
  
  return (
    <Image 
      {...props} 
      src={processedSrc} 
      alt={props.alt || ''}
    />
  );
};

export default forwardRef(MyImage);
