import FingerprintJS from '@fingerprintjs/fingerprintjs';

export const getUserFingerprint = async () => {
  const fp = await FingerprintJS.load();
  const result = await fp.get();
  console.log(result.visitorId);
};

export const subRoute = import.meta.env.VITE_BASE ? `${import.meta.env.VITE_BASE}/` : '/';

export const getWebReqUrl = (url: string = '') => {
  if (!url) return '/';
  const baseUrl = import.meta.env.VITE_API_BASE_URL;
  if (!baseUrl) return url;

  if (!url.startsWith('/') || url.startsWith(baseUrl)) return url;
  return `${baseUrl}${url}`;
};

export const getStaticUrl = (url: string = '') => {
  if (!url) return '/';
  const baseUrl = import.meta.env.VITE_BASE;
  if (!baseUrl) return url;

  if (!url.startsWith('/') || url.startsWith(baseUrl)) return url;
  return `${baseUrl}${url}`;
};

export const isMobile = () => {
  // SSR return false
  if (typeof window === 'undefined') return false;

  // 1. Check User-Agent
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android',
    'iphone',
    'ipod',
    'ipad',
    'windows phone',
    'blackberry',
    'webos',
    'iemobile',
    'opera mini'
  ];
  const isMobileUA = mobileKeywords.some((keyword) => userAgent.includes(keyword));

  // 2. Check screen width
  const isMobileWidth = window.innerWidth <= 900;

  // 3. Check if touch events are supported (exclude touch screen PCs)
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  // If any of the following conditions are met, it is considered a mobile device
  return isMobileUA || (isMobileWidth && isTouchDevice);
};
