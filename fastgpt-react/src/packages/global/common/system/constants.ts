// export const HUMAN_ICON = `/icon/human.svg`;
// export const LOGO_ICON = `/icon/logo.svg`;
// export const HUGGING_FACE_ICON = `/imgs/model/huggingface.svg`;

// export const DEFAULT_TEAM_AVATAR = `/imgs/avatar/defaultTeamAvatar.svg`;
// export const DEFAULT_ORG_AVATAR = '/imgs/avatar/defaultOrgAvatar.svg';
// export const DEFAULT_USER_AVATAR = '/imgs/avatar/BlueAvatar.svg';
export const HUMAN_ICON = `data:image/svg+xml;base64,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`;
export const LOGO_ICON = `data:image/svg+xml;base64,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`;
export const HUGGING_FACE_ICON = `data:image/svg+xml;base64,PHN2ZyB0PSIxNzEwODQxMjcyODg0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjI2ODQiCiAgICB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+CiAgICA8cGF0aAogICAgICAgIGQ9Ik01MTEuOTY4IDk1OS45MzZjMjk4LjY4OCAwIDQ0Ny45NjgtMjAwLjU3NiA0NDcuOTY4LTQ0OCAwLTI0Ny4zNi0xNDkuMjgtNDQ3LjkzNi00NDgtNDQ3LjkzNkMyMTMuMjggNjQgNjQgMjY0LjU3NiA2NCA1MTEuOTY4YzAgMjQ3LjM5MiAxNDkuMjQ4IDQ0Ny45NjggNDQ3Ljk2OCA0NDcuOTY4eiIKICAgICAgICBmaWxsPSIjRkZCMDJFIiBwLWlkPSIyNjg1Ij48L3BhdGg+CiAgICA8cGF0aAogICAgICAgIGQ9Ik0xMDMuOTM2IDU4Ni45MTJhMzEuOTM2IDMxLjkzNiAwIDAgMC03LjU4NCAyNS41NjggMzIgMzIgMCAwIDAtMzcuMTUyIDUxLjg0bDkuMzQ0IDhhMzIgMzIgMCAwIDAtMjQuOTkyIDU2LjI1Nmw2My41MiA1Mi45MjgtNC4wMzItMS45ODRhMzUuNzEyIDM1LjcxMiAwIDAgMC0zNi42NzIgNjAuODk2QzEwNy43MTIgODY5Ljc2IDE2My4wMDggOTA4LjY0IDE5MiA5MjhjNDggMzIgMTAyLjcyIDQyLjk0NCAxNjAgMCAzMi0yNCA3Mi40OC05Ny45ODQgMjkuOTItMTcxLjcxMi04LjA2NC0xMy45NTItMTUuMjk2LTI4LjY0LTE4LjMwNC00NC40OC0xMy4xNTItNjkuNzYtMzIuOC0xNDEuMjE2LTc1LjYxNi0xMTkuODA4LTIzLjIgMTEuNTg0LTIxLjE4NCAzMS41ODQtMTguMzA0IDYwIDEuMDg4IDEwLjc4NCAyLjMwNCAyMi43ODQgMi4zMDQgMzZsLTIuNTYgMS4yOC0xMjAuMzg0LTEwNS4zNzZhMzIgMzIgMCAwIDAtNDUuMTIgMy4wNHpNOTIwLjA5NiA1ODYuOTEyYzYuMzY4IDcuMjk2IDguODMyIDE2LjY0IDcuNTg0IDI1LjU2OGEzMiAzMiAwIDAgMSAzNy4xMiA1MS44NGwtOS4zNDQgOGEzMiAzMiAwIDAgMSAyNS4wMjQgNTYuMjU2bC02My41MiA1Mi45MjggNC4wMzItMS45ODRhMzUuNzEyIDM1LjcxMiAwIDAgMSAzNi42NzIgNjAuODk2QzkxNi4zMiA4NjkuNzYgODYxLjAyNCA5MDguNjQgODMyIDkyOGMtNDggMzItMTAyLjc1MiA0Mi45NDQtMTYwIDAtMzItMjQtNzIuNDgtOTcuOTg0LTI5LjkyLTE3MS43MTIgOC4wNjQtMTMuOTUyIDE1LjI5Ni0yOC42NCAxOC4zMDQtNDQuNDggMTMuMTUyLTY5Ljc2IDMyLjgtMTQxLjIxNiA3NS42MTYtMTE5LjgwOCAyMy4yIDExLjU4NCAyMS4xODQgMzEuNTg0IDE4LjMwNCA2MC0xLjA4OCAxMC43ODQtMi4zMDQgMjIuNzg0LTIuMzA0IDM2bDIuNTYgMS4yOCAxMjAuMzg0LTEwNS4zNzZhMzIgMzIgMCAwIDEgNDUuMTIgMy4wNHoiCiAgICAgICAgZmlsbD0iI0ZGODIyRCIgcC1pZD0iMjY4NiI+PC9wYXRoPgogICAgPHBhdGgKICAgICAgICBkPSJNMjI0IDQ2NGMwIDQ0LjE2LTI4LjY0IDgwLTY0IDgwcy02NC0zNS44NC02NC04MCAyOC42NC04MCA2NC04MCA2NCAzNS44NCA2NCA4MHpNOTI4IDQ2NGMwIDQ0LjE2LTI4LjY0IDgwLTY0IDgwcy02NC0zNS44NC02NC04MCAyOC42NC04MCA2NC04MCA2NCAzNS44NCA2NCA4MHoiCiAgICAgICAgZmlsbD0iI0ZGNjcyMyIgcC1pZD0iMjY4NyI+PC9wYXRoPgogICAgPHBhdGgKICAgICAgICBkPSJNMjk5LjE2OCAzMzMuMTg0Yy02LjcyIDcuMjk2LTEwLjI0IDE3LjAyNC0xMS43NDQgMjQuOTI4YTMyIDMyIDAgMCAxLTYyLjg0OC0xMi4yMjRjMi44NDgtMTQuNTkyIDkuOTItMzYuODk2IDI3LjQ1Ni01NS45NjhDMjcwLjQ5NiAyNjkuNzkyIDI5OC4xMTIgMjU2IDMzNiAyNTZjMzguMjQgMCA2NS45ODQgMTQuNDY0IDg0LjM1MiAzNC42MjQgMTcuNDA4IDE5LjEwNCAyNC42NCA0MS4zNDQgMjcuMiA1NS45MDRhMzIgMzIgMCAwIDEtNjMuMDcyIDEwLjk0NCA0OS40NzIgNDkuNDcyIDAgMCAwLTExLjQ1Ni0yMy43NDRDMzY3LjA0IDMyNy4xMDQgMzU2LjU0NCAzMjAgMzM2IDMyMGMtMjAuODk2IDAtMzEuMTA0IDYuOTQ0LTM2LjgzMiAxMy4xODR6TTY1MS4yIDMzMy4xODRjLTYuNzIgNy4yOTYtMTAuMjQgMTcuMDI0LTExLjc3NiAyNC45MjhhMzIgMzIgMCAwIDEtNjIuODE2LTEyLjIyNGMyLjgxNi0xNC41OTIgOS45Mi0zNi44OTYgMjcuNDI0LTU1Ljk2OEM2MjIuNDk2IDI2OS43OTIgNjUwLjExMiAyNTYgNjg4IDI1NmMzOC4yNzIgMCA2NS45ODQgMTQuNDY0IDg0LjM1MiAzNC42MjQgMTcuNDA4IDE5LjEwNCAyNC42NCA0MS4zNDQgMjcuMiA1NS45MDRhMzIgMzIgMCAwIDEtNjMuMDcyIDEwLjk0NCA0OS40NCA0OS40NCAwIDAgMC0xMS40NTYtMjMuNzQ0QzcxOS4wNCAzMjcuMTA0IDcwOC41NDQgMzIwIDY4OCAzMjBjLTIwLjg5NiAwLTMxLjA3MiA2Ljk0NC0zNi44IDEzLjE4NHpNMzEzLjYgNDkyLjhhMzIgMzIgMCAxIDAtNTEuMiAzOC40YzIyLjQ2NCAyOS45NTIgOTYuMjU2IDkyLjggMjQ5LjYgOTIuOHMyMjcuMTM2LTYyLjg0OCAyNDkuNi05Mi44YTMyIDMyIDAgMCAwLTUxLjItMzguNGMtOS41MzYgMTIuNzA0LTYzLjc0NCA2Ny4yLTE5OC40IDY3LjJzLTE4OC44NjQtNTQuNDk2LTE5OC40LTY3LjJ6IgogICAgICAgIGZpbGw9IiM0MDJBMzIiIHAtaWQ9IjI2ODgiPjwvcGF0aD4KPC9zdmc+`;

export const DEFAULT_TEAM_AVATAR = `data:image/svg+xml;base64,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`;
export const DEFAULT_ORG_AVATAR = 'data:image/svg+xml;base64,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';
export const DEFAULT_USER_AVATAR = 'data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMzYgMzYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMTgiIHI9IjE4IiBmaWxsPSIjMTFCNkZDIi8+CjxwYXRoIGQ9Ik0xOC4zNjYgMjAuOTk5OEMxNS43MjQyIDIwLjk5OTggMTMuMzc1IDIyLjI3NTQgMTEuODc5MyAyNC4yNTQ4QzExLjU1NzQgMjQuNjgwOCAxMS4zOTY0IDI0Ljg5MzkgMTEuNDAxNyAyNS4xODE3QzExLjQwNTggMjUuNDA0MiAxMS41NDU0IDI1LjY4NDcgMTEuNzIwNCAyNS44MjIxQzExLjk0NyAyNS45OTk4IDEyLjI2MDkgMjUuOTk5OCAxMi44ODg3IDI1Ljk5OThIMjMuODQzM0MyNC40NzExIDI1Ljk5OTggMjQuNzg1IDI1Ljk5OTggMjUuMDExNSAyNS44MjIxQzI1LjE4NjUgMjUuNjg0NyAyNS4zMjYyIDI1LjQwNDIgMjUuMzMwMiAyNS4xODE3QzI1LjMzNTUgMjQuODkzOSAyNS4xNzQ1IDI0LjY4MDggMjQuODUyNiAyNC4yNTQ4QzIzLjM1NyAyMi4yNzU0IDIxLjAwNzcgMjAuOTk5OCAxOC4zNjYgMjAuOTk5OFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xOC4zNjYgMTguNDk5OEMyMC40MzcgMTguNDk5OCAyMi4xMTYgMTYuODIwOSAyMi4xMTYgMTQuNzQ5OEMyMi4xMTYgMTIuNjc4OCAyMC40MzcgMTAuOTk5OCAxOC4zNjYgMTAuOTk5OEMxNi4yOTQ5IDEwLjk5OTggMTQuNjE2IDEyLjY3ODggMTQuNjE2IDE0Ljc0OThDMTQuNjE2IDE2LjgyMDkgMTYuMjk0OSAxOC40OTk4IDE4LjM2NiAxOC40OTk4WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xOC4zNjYgMTEuODMzMkMxNi43NTUxIDExLjgzMzIgMTUuNDQ5MyAxMy4xMzkgMTUuNDQ5MyAxNC43NDk4QzE1LjQ0OTMgMTYuMzYwNyAxNi43NTUxIDE3LjY2NjUgMTguMzY2IDE3LjY2NjVDMTkuOTc2OCAxNy42NjY1IDIxLjI4MjYgMTYuMzYwNyAyMS4yODI2IDE0Ljc0OThDMjEuMjgyNiAxMy4xMzkgMTkuOTc2OCAxMS44MzMyIDE4LjM2NiAxMS44MzMyWk0xMy43ODI2IDE0Ljc0OThDMTMuNzgyNiAxMi4yMTg1IDE1LjgzNDcgMTAuMTY2NSAxOC4zNjYgMTAuMTY2NUMyMC44OTczIDEwLjE2NjUgMjIuOTQ5MyAxMi4yMTg1IDIyLjk0OTMgMTQuNzQ5OEMyMi45NDkzIDE3LjI4MTEgMjAuODk3MyAxOS4zMzMyIDE4LjM2NiAxOS4zMzMyQzE1LjgzNDcgMTkuMzMzMiAxMy43ODI2IDE3LjI4MTEgMTMuNzgyNiAxNC43NDk4Wk0xOC4zNjYgMjEuODMzMkMxNi4wMDAxIDIxLjgzMzIgMTMuODkxNCAyMi45NzQyIDEyLjU0NDIgMjQuNzU3MkMxMi40NjE4IDI0Ljg2NjMgMTIuMzk4NyAyNC45NDk4IDEyLjM0NjIgMjUuMDIzM0MxMi4zMDY0IDI1LjA3OSAxMi4yNzkyIDI1LjEyIDEyLjI2MDMgMjUuMTUxQzEyLjM4NDEgMjUuMTY1MiAxMi41NjA2IDI1LjE2NjUgMTIuODg4NyAyNS4xNjY1SDIzLjg0MzNDMjQuMTcxMyAyNS4xNjY1IDI0LjM0NzggMjUuMTY1MiAyNC40NzE3IDI1LjE1MUMyNC40NTI3IDI1LjEyIDI0LjQyNTUgMjUuMDc5IDI0LjM4NTcgMjUuMDIzM0MyNC4zMzMzIDI0Ljk0OTggMjQuMjcwMiAyNC44NjYzIDI0LjE4NzggMjQuNzU3MkMyMi44NDA2IDIyLjk3NDIgMjAuNzMxOCAyMS44MzMyIDE4LjM2NiAyMS44MzMyWk0xMS4yMTQ0IDIzLjc1MjRDMTIuODU4NiAyMS41NzY1IDE1LjQ0ODQgMjAuMTY2NSAxOC4zNjYgMjAuMTY2NUMyMS4yODM2IDIwLjE2NjUgMjMuODczNCAyMS41NzY1IDI1LjUxNzUgMjMuNzUyNEMyNS41MjQ5IDIzLjc2MjMgMjUuNTMyNCAyMy43NzIxIDI1LjUzOTkgMjMuNzgyMUMyNS42ODEzIDIzLjk2OSAyNS44MzI4IDI0LjE2OTQgMjUuOTQxMiAyNC4zNjI4QzI2LjA3MjMgMjQuNTk2OSAyNi4xNjk1IDI0Ljg2NzcgMjYuMTYzNCAyNS4xOTdDMjYuMTU4NiAyNS40NjE3IDI2LjA3ODQgMjUuNzA5NSAyNS45ODEgMjUuOTA1MkMyNS44ODM2IDI2LjEwMDggMjUuNzM0MiAyNi4zMTQyIDI1LjUyNTkgMjYuNDc3N0MyNS4yNDgyIDI2LjY5NTYgMjQuOTQ1OCAyNi43NzI4IDI0LjY3NzEgMjYuODA1QzI0LjQ0MTQgMjYuODMzMyAyNC4xNjIxIDI2LjgzMzIgMjMuODgxMiAyNi44MzMyQzIzLjg2ODYgMjYuODMzMiAyMy44NTU5IDI2LjgzMzIgMjMuODQzMyAyNi44MzMySDEyLjg4ODdDMTIuODc2IDI2LjgzMzIgMTIuODYzMyAyNi44MzMyIDEyLjg1MDcgMjYuODMzMkMxMi41Njk5IDI2LjgzMzIgMTIuMjkwNSAyNi44MzMzIDEyLjA1NDggMjYuODA1QzExLjc4NjIgMjYuNzcyOCAxMS40ODM4IDI2LjY5NTYgMTEuMjA2IDI2LjQ3NzdDMTAuOTk3NyAyNi4zMTQyIDEwLjg0ODMgMjYuMTAwOCAxMC43NTA5IDI1LjkwNTJDMTAuNjUzNSAyNS43MDk1IDEwLjU3MzMgMjUuNDYxNyAxMC41Njg1IDI1LjE5N0MxMC41NjI1IDI0Ljg2NzcgMTAuNjU5NyAyNC41OTY5IDEwLjc5MDggMjQuMzYyOEMxMC44OTkxIDI0LjE2OTQgMTEuMDUwNyAyMy45NjkgMTEuMTkyIDIzLjc4MjFDMTEuMTk5NSAyMy43NzIxIDExLjIwNyAyMy43NjIzIDExLjIxNDQgMjMuNzUyNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=';

export const isProduction = import.meta.env.VITE_NODE_ENV === 'production';
export const isTestEnv = import.meta.env.VITE_NODE_ENV === 'test';
