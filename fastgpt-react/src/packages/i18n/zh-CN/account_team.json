{"1person": "1人", "1year": "1年", "30mins": "30分钟", "7days": "7天", "accept": "接受", "action": "操作", "admin_add_plan": "添加团队套餐", "admin_add_user": "添加用户", "admin_change_license": "变更许可证", "admin_create_app_template": "添加模板", "admin_create_plugin": "添加插件", "admin_create_plugin_group": "创建插件分组", "admin_delete_app_template": "删除模板", "admin_delete_plugin": "插件删除", "admin_delete_plugin_group": "删除插件分组", "admin_delete_template_type": "删除模板分类", "admin_finish_invoice": "开具发票", "admin_login": "管理员登录", "admin_save_template_type": "更新模板分类", "admin_send_system_inform": "发送系统通知", "admin_update_app_template": "更新模板", "admin_update_plan": "编辑团队套餐", "admin_update_plugin": "插件更新", "admin_update_plugin_group": "插件分组更新", "admin_update_system_config": "系统配置更新", "admin_update_system_modal": "系统公告配置", "admin_update_team": "编辑团队信息", "admin_update_user": "编辑用户信息", "assign_permission": "权限变更", "audit_log": "审计", "change_department_name": "组织结构编辑", "change_member_name": "用户改名", "change_member_name_self": "变更用户名", "change_notification_settings": "变更通知接收途径", "change_password": "更改密码", "confirm_delete_from_org": "确认将 {{username}} 移出组织结构？", "confirm_delete_from_team": "确认将 {{username}} 移出团队？", "confirm_delete_group": "确认删除角色？", "confirm_delete_org": "确认删除该组织结构？", "confirm_forbidden": "确认停用", "confirm_leave_team": "确认离开该团队？  \n退出后，您在该团队所有的资源均转让给团队所有者。", "copy_link": "复制链接", "create_api_key": "创建api密钥", "create_app": "创建应用", "create_app_copy": "创建应用副本", "create_app_folder": "创建应用文件夹", "create_app_publish_channel": "创建分享渠道", "create_collection": "创建集合", "create_data": "插入数据", "create_dataset": "创建知识库", "create_dataset_folder": "创建知识库文件夹", "create_department": "创建子组织结构", "create_group": "创建角色", "create_invitation_link": "创建邀请链接", "create_invoice": "开发票", "create_org": "创建组织结构", "create_sub_org": "创建子组织结构", "dataset.api_file": "API导入", "dataset.common_dataset": "知识库", "dataset.external_file": "外部文件", "dataset.feishu_dataset": "飞书多维表格", "dataset.folder_dataset": "文件夹", "dataset.website_dataset": "网站同步", "dataset.yuque_dataset": "语雀知识库", "delete": "删除", "delete_api_key": "删除api密钥", "delete_app": "删除工作台应用", "delete_app_collaborator": "应用权限删除", "delete_app_publish_channel": "删除发布渠道", "delete_collection": "删除集合", "delete_data": "删除数据", "delete_dataset": "删除知识库", "delete_dataset_collaborator": "知识库权限删除", "delete_department": "删除子组织结构", "delete_from_org": "移出组织结构", "delete_from_team": "移出团队", "delete_group": "删除角色", "delete_org": "删除组织结构", "department": "组织结构", "edit_info": "编辑信息", "edit_member": "编辑用户", "edit_member_tip": "用户名", "edit_org_info": "编辑组织结构信息", "expires": "过期时间", "export_app_chat_log": "导出应用聊天记录", "export_bill_records": "导出账单记录", "export_dataset": "导出知识库", "export_members": "导出用户", "forbid_hint": "停用后，该邀请链接将失效。 该操作不可撤销，是否确认停用？", "forbid_success": "停用成功", "forbidden": "停用", "group": "角色", "group_name": "角色名称", "handle_invitation": "处理团队邀请", "has_forbidden": "已失效", "has_invited": "已邀请", "ignore": "忽略", "inform_level_common": "一般", "inform_level_emergency": "紧急", "inform_level_important": "重要", "invitation_copy_link": "【{{systemName}}】 {{userName}} 邀请您加入{{teamName}}团队，链接：{{url}}", "invitation_link_auto_clean_hint": "已失效链接将在30天后自动清理", "invitation_link_description": "链接描述", "invitation_link_list": "链接列表", "invite_member": "邀请用户", "invited": "已邀请", "join_team": "加入团队", "join_update_time": "加入/更新时间", "kick_out_team": "移除用户", "label_sync": "标签同步", "leave": "已离职", "leave_team_failed": "离开团队异常", "log_admin_add_plan": "【{{name}}】将给团队id为【{{teamId}}】的团队添加了套餐", "log_admin_add_user": "【{{name}}】创建了一个名为【{{userName}}】的用户", "log_admin_change_license": "【{{name}}】变更了License", "log_admin_create_app_template": "【{{name}}】添加了名为【{{templateName}}】的模板", "log_admin_create_plugin": "【{{name}}】添加了名为【{{pluginName}}】的插件", "log_admin_create_plugin_group": "【{{name}}】创建了名为【{{groupName}}】的插件分组", "log_admin_delete_app_template": "【{{name}}】删除了名为【{{templateName}}】的模板", "log_admin_delete_plugin": "【{{name}}】删除了名为【{{pluginName}}】的插件", "log_admin_delete_plugin_group": "【{{name}}】删除了名为【{{groupName}}】的插件分组", "log_admin_delete_template_type": "【{{name}}】删除了名为【{{typeName}}】的模板分类", "log_admin_finish_invoice": "【{{name}}】给名为【{{teamName}}】的团队开具了发票", "log_admin_login": "【{{name}}】登录了管理员后台", "log_admin_save_template_type": "【{{name}}】添加了名为【{{typeName}}】的模板分类", "log_admin_send_system_inform": "【{{name}}】发送了标题为【{{informTitle}}】的系统通知，等级为【{{level}}】", "log_admin_update_app_template": "【{{name}}】更新了名为【{{templateName}}】的模板信息", "log_admin_update_plan": "【{{name}}】编辑了团队id为【{{teamId}}】的团队的套餐信息", "log_admin_update_plugin": "【{{name}}】更新了名为【{{pluginName}}】的插件信息", "log_admin_update_plugin_group": "【{{name}}】更新了名为【{{groupName}}】的插件分组", "log_admin_update_system_config": "【{{name}}】更新了系统配置", "log_admin_update_system_modal": "【{{name}}】进行了系统公告配置", "log_admin_update_team": "【{{name}}】将名为【{{teamName}}】的团队编辑信息为团队名：【{{newTeamName}}】，余额：【{{newBalance}}】", "log_admin_update_user": "修改【{{userName}}】的用户信息", "log_assign_permission": "【{{name}}】更新了【{{objectName}}】的权限：[应用创建:【{{appCreate}}】, 知识库:【{{datasetCreate}}】, API密钥:【{{apiKeyCreate}}】, 管理:【{{manage}}】]", "log_change_department": "【{{name}}】更新了组织结构【{{departmentName}}】", "log_change_member_name": "【{{name}}】将用户【{{memberName}}】重命名为【{{newName}}】", "log_change_member_name_self": "【{{name}}】把自己的用户名从【{{oldName}}】变更为【{{newName}}】", "log_change_notification_settings": "【{{name}}】进行了变更通知接收途径操作", "log_change_password": "【{{name}}】进行了变更密码操作", "log_create_api_key": "【{{name}}】创建了名为【{{keyName}}】的api密钥", "log_create_app": "【{{name}}】创建了名为【{{appName}}】的【{{appType}}】", "log_create_app_copy": "【{{name}}】给名为【{{appName}}】的【{{appType}}】创建了一个副本", "log_create_app_folder": "【{{name}}】创建了名为【{{folderName}}】的文件夹", "log_create_app_publish_channel": "【{{name}}】给名为【{{appName}}】的【{{appType}}】创建了名为【{{channelName}}】的渠道", "log_create_collection": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】创建了名为【{{collectionName}}】的集合", "log_create_data": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】往名为【{{collectionName}}】的集合插入数据", "log_create_dataset": "【{{name}}】创建了名为【{{datasetName}}】的【{{datasetType}}】", "log_create_dataset_folder": "【{{name}}】创建了名为{{folderName}}】的文件夹", "log_create_department": "【{{name}}】创建了组织结构【{{departmentName}}】", "log_create_group": "【{{name}}】创建了角色【{{groupName}}】", "log_create_invitation_link": "【{{name}}】创建了邀请链接【{{link}}】", "log_create_invoice": "【{{name}}】进行了开发票操作", "log_delete_api_key": "【{{name}}】删除了名为【{{keyName}}】的api密钥", "log_delete_app": "【{{name}}】将名为【{{appName}}】的【{{appType}}】删除", "log_delete_app_collaborator": "【{{name}}】将名为【{{appName}}】的【{{appType}}】中名为【{{itemValueName}}】的【{{itemName}}】权限删除", "log_delete_app_publish_channel": "【{{name}}】名为【{{appName}}】的【{{appType}}】删除了名为【{{channelName}}】的渠道", "log_delete_collection": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】删除了名为【{{collectionName}}】的集合", "log_delete_data": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】在名为【{{collectionName}}】的集合删除数据", "log_delete_dataset": "【{{name}}】删除了名为【{{datasetName}}】的【{{datasetType}}】", "log_delete_dataset_collaborator": "【{{name}}】将名为【{{datasetName}}】的【{{datasetType}}】中名为【itemValueName】的【itemName】权限删除", "log_delete_department": "【{{name}}】删除了组织结构【{{departmentName}}】", "log_delete_group": "【{{name}}】删除了角色【{{groupName}}】", "log_details": "详情", "log_export_app_chat_log": "【{{name}}】导出了名为【{{appName}}】的【{{appType}}】的聊天记录", "log_export_bill_records": "【{{name}}】导出了账单记录", "log_export_dataset": "【{{name}}】导出了名为【{{datasetName}}】的【{{datasetType}}】", "log_join_team": "【{{name}}】通过邀请链接【{{link}}】加入团队", "log_kick_out_team": "【{{name}}】移除了用户【{{memberName}}】", "log_login": "【{{name}}】登录了系统", "log_move_app": "【{{name}}】将名为【{{appName}}】的【{{appType}}】移动到【{{targetFolderName}}】", "log_move_dataset": "【{{name}}】将名为【{{datasetName}}】的【{{datasetType}}】移动到【{{targetFolderName}}】", "log_purchase_plan": "【{{name}}】购买了套餐", "log_recover_team_member": "【{{name}}】恢复了用户【{{memberName}}】", "log_relocate_department": "【{{name}}】移动了组织结构【{{departmentName}}】", "log_retrain_collection": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】重新训练了名为【{{collectionName}}】的集合", "log_search_test": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】执行搜索测试操作", "log_set_invoice_header": "【{{name}}】进行了设置发票抬头操作", "log_time": "操作时间", "log_transfer_app_ownership": "【{{name}}】将名为【{{appName}}】的【{{appType}}】的所有权从【{{oldOwnerName}}】转移到【{{newOwnerName}}】", "log_transfer_dataset_ownership": "【{{name}}】将名为【{{datasetName}}】的【{{datasetType}}】的所有权从【{{oldOwnerName}}】转移到【{{newOwnerName}}】", "log_type": "操作类型", "log_update_api_key": "【{{name}}】更新了名为【{{keyName}}】的api密钥", "log_update_app_collaborator": "【{{name}}】将名为【{{appName}}】的【{{appType}}】的合作者更新为：组织：【{{orgList}}】，角色：【{{groupList}}】，用户【{{tmbList}}】；权限更新为：读权限：【{{readPermission}}】，写权限：【{{writePermission}}】，管理员权限：【{{managePermission}}】", "log_update_app_info": "【{{name}}】更新了名为【{{appName}}】的【{{appType}}】:【{{newItemNames}}】为【{{newItemValues}}】", "log_update_app_publish_channel": "【{{name}}】给名为【{{appName}}】的【{{appType}}】更新了名为【{{channelName}}】的渠道", "log_update_collection": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】更新了名为【{{collectionName}}】的集合", "log_update_data": "【{{name}}】在名为【{{datasetName}}】的【{{datasetType}}】在名为【{{collectionName}}】的集合更新数据", "log_update_dataset": "【{{name}}】更新了名为【{{datasetName}}】的【{{datasetType}}】", "log_update_dataset_collaborator": "【{{name}}】将名为【{{datasetName}}】的【{{datasetType}}】的合作者更新为：组织：【{{orgList}}】，角色：【{{groupList}}】，用户【{{tmbList}}】；权限更新为：【{{readPermission}}】，【{{writePermission}}】，【{{managePermission}}】", "log_update_publish_app": "【{{name}}】【{{operationName}}】名为【{{appName}}】的【{{appType}}】", "log_user": "操作人员", "login": "登录", "manage_member": "管理用户", "member": "用户", "member_group": "所属角色", "move_app": "应用位置移动", "move_dataset": "移动知识库", "move_member": "移动用户", "move_org": "移动组织结构", "notification_recieve": "团队通知接收", "org": "组织结构", "org_description": "介绍", "org_name": "组织结构名称", "owner": "所有者", "permission": "权限", "permission_apikeyCreate": "创建 API 密钥", "permission_apikeyCreate_Tip": "可以创建全局的 APIKey和 MCP 服务", "permission_appCreate": "创建应用", "permission_appCreate_tip": "可以在根目录创建应用，（文件夹下的创建权限由文件夹控制）", "permission_datasetCreate": "创建知识库", "permission_datasetCreate_Tip": "可以在根目录创建知识库，（文件夹下的创建权限由文件夹控制）", "permission_manage": "管理员", "permission_manage_tip": "可以管理用户、创建角色、管理所有角色、为角色和用户分配权限", "please_bind_contact": "请绑定联系方式", "purchase_plan": "升级套餐", "recover_team_member": "用户恢复", "relocate_department": "组织结构移动", "remark": "备注", "remove_tip": "确认将 {{username}} 移出团队？用户将被标记为“已离职”，不删除操作数据，账号下资源自动转让给团队所有者。", "restore_tip": "确认将 {{username}} 加入团队吗？仅恢复该用户账号可用性及相关权限，无法恢复账号下资源。", "restore_tip_title": "恢复确认", "retain_admin_permissions": "保留管理员权限", "retrain_collection": "重新训练集合", "save_and_publish": "保存并发布", "search_log": "搜索日志", "search_member": "搜索用户", "search_member_group_name": "搜索用户/角色名称", "search_org": "搜索组织结构", "search_test": "搜索测试", "set_invoice_header": "设置发票抬头", "set_name_avatar": "团队头像 & 团队名", "sync_immediately": "立即同步", "sync_member_failed": "同步用户失败", "sync_member_success": "同步用户成功", "total_team_members": "共 {{amount}} 名用户", "transfer_app_ownership": "转移应用所有权", "transfer_dataset_ownership": "转移知识库所有权", "transfer_ownership": "转让所有者", "type.Folder": "文件夹", "type.Http plugin": "HTTP 插件", "type.Plugin": "插件", "type.Simple bot": "简易应用", "type.Tool": "工具", "type.Tool set": "工具集", "type.Workflow bot": "工作流", "unlimited": "无限制", "update": "更新", "update_api_key": "更新api密钥", "update_app_collaborator": "应用权限更改", "update_app_info": "应用信息修改", "update_app_publish_channel": "更新发布渠道", "update_collection": "更新集合", "update_data": "更新数据", "update_dataset": "更新知识库", "update_dataset_collaborator": "知识库权限更改", "update_publish_app": "应用更新", "used_times_limit": "有效人数", "user_name": "用户名", "user_team_invite_member": "邀请用户", "user_team_leave_team": "离开团队", "user_team_leave_team_failed": "离开团队失败", "waiting": "待接受"}