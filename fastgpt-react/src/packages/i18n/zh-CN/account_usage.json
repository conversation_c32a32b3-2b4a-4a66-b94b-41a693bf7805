{"ai_model": "AI 模型", "all": "所有", "app_name": "应用名", "auto_index": "索引增强", "billing_module": "扣费模块", "confirm_export": "共筛选出 {{total}} 条数据，是否确认导出？", "current_filter_conditions": "当前筛选条件：", "dashboard": "仪表盘", "details": "详情", "dingtalk": "钉钉", "duration_seconds": "时长（秒）", "embedding_index": "索引生成", "every_day": "天", "every_month": "月", "every_week": "每周", "export_confirm": "导出确认", "export_confirm_tip": "当前共 {{total}} 条使用记录，确认导出？", "export_success": "导出成功", "export_title": "时间,用户,类型,项目名,AI 积分消耗", "feishu": "飞书", "generation_time": "生成时间", "image_index": "图片索引", "image_parse": "图片标注", "input_token_length": "输入 tokens", "llm_paragraph": "模型分段", "mcp": "MCP 调用", "member": "用户", "member_name": "用户名", "module_name": "模块名", "month": "月", "no_usage_records": "暂无使用记录", "official_account": "公众号", "order_number": "订单号", "output_token_length": "输出 tokens", "pages": "页数", "pdf_enhanced_parse": "PDF 增强解析", "pdf_parse": "PDF 解析", "points": "积分", "project_name": "项目名", "qa": "问答对提取", "select_member_and_source_first": "请先选中用户和类型", "share": "分享链接", "source": "来源", "text_length": "文本长度", "token_length": "token 长度", "total_points": "AI 积分消耗", "total_points_consumed": "AI 积分消耗", "total_usage": "总消耗", "usage_detail": "使用详情", "user_type": "类型", "wecom": "企业微信"}