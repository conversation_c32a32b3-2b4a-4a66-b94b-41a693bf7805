{"Action": "操作", "Add": "添加", "Add_new_input": "新增输入", "All": "全部", "App": "应用", "Cancel": "取消", "Choose": "选择", "Click_to_expand": "点击查看详情", "Close": "关闭", "Code": "源码", "Config": "配置", "Confirm": "确认", "Continue_Adding": "继续添加", "Copy": "复制", "Creating": "创建中", "Delete": "删除", "Detail": "详情", "Documents": "文档", "Done": "完成", "Download": "下载", "Edit": "编辑", "Error": "错误", "Exit": "退出", "Export": "导出", "FAQ.ai_point_a": "每次调用AI模型时，都会消耗一定的AI积分。具体的计算标准可参考上方的“AI 积分计算标准”。系统会优先采用模型厂商返回的实际 usage，若为空，则采用GPT3.5的计算方式进行估算，1Token≈0.7中文字符≈0.9英文单词，连续出现的字符可能被认为是1个Tokens。", "FAQ.ai_point_expire_a": "会过期。当前套餐过期后，AI积分将会清空，并更新为新套餐的AI积分。年度套餐的AI积分时长为1年，而不是每个月。", "FAQ.ai_point_expire_q": "AI积分会过期么？", "FAQ.ai_point_q": "什么是AI积分？", "FAQ.check_subscription_a": "账号-个人信息-套餐详情-使用情况。您可以查看所拥有套餐的生效和到期时间。当付费套餐到期后将自动切换免费版。", "FAQ.check_subscription_q": "在哪里查看已订阅的套餐？", "FAQ.dataset_compute_a": "1条知识库存储等于1条知识库索引。一条分块数据，通常对应多条索引，可以在单个知识库集合中看到\"n组索引\"", "FAQ.dataset_compute_q": "知识库存储怎么计算？", "FAQ.dataset_index_a": "不会。但知识库索引超出时，无法插入和更新知识库内容。", "FAQ.dataset_index_q": "知识库索引超出会删除么？", "FAQ.free_user_clean_a": "免费版团队（免费版且未购买额外套餐）连续 30 天未登录系统，系统会自动清除该团队下所有知识库内容。", "FAQ.free_user_clean_q": "免费版数据会清除么？", "FAQ.package_overlay_a": "可以的。每次购买的资源包都是独立的，在其有效期内将会叠加使用。AI积分会优先扣除最先过期的资源包。", "FAQ.package_overlay_q": "额外资源包可以叠加么？", "FAQ.switch_package_a": "套餐使用规则为优先使用更高级的套餐，因此，购买的新套餐若比当前套餐更高级，则新套餐立即生效：否则将继续使用当前套餐。", "FAQ.switch_package_q": "是否切换订阅套餐？", "File": "文件", "Finish": "完成", "Folder": "文件夹", "FullScreen": "全屏", "FullScreenLight": "全屏预览", "Import": "导入", "Input": "输入", "Instructions": "使用说明", "Intro": "介绍", "Loading": "加载中...", "Login": "登录", "More": "更多", "Move": "移动", "Name": "名称", "None": "无", "OK": "好的", "Open": "打开", "Operation": "操作", "Other": "其他", "Output": "输出", "Params": "参数", "Parse": "解析", "Permission": "权限", "Permission_tip": "个人权限大于角色权限", "Preview": "预览", "Remove": "移除", "Rename": "重命名", "Required_input": "必填", "Reset": "恢复默认", "Restart": "重新开始", "Resume": "恢复", "Role": "权限", "Run": "运行", "Running": "运行中", "Save": "保存", "Save_and_exit": "保存并退出", "Search": "搜索", "Select_all": "全选", "Setting": "设置", "Status": "状态", "Submit": "提交", "Success": "成功", "Team": "团队", "UnKnow": "未知", "Unlimited": "无限制", "Update": "更新", "Username": "用户名", "Waiting": "等待中", "Warning": "警告", "Website": "网站", "action_confirm": "操作确认", "add_new": "新增", "add_new_param": "新增参数", "add_success": "添加成功", "all_quotes": "全部引用", "all_result": "完整结果", "app_not_version": " 该应用未发布过，请先发布应用", "auth_config": "鉴权配置", "auth_type": "鉴权类型", "auth_type.Custom": "自定义", "auth_type.None": "无", "back": "返回", "base_config": "基础配置", "bill_already_processed": "订单已处理", "bill_expired": "订单已过期", "bill_not_pay_processed": "非在线订单", "button.extra_dataset_size_tip": "您正在购买【额外知识库容量】", "button.extra_points_tip": "您正在购买【额外 AI 积分】", "can_copy_content_tip": "无法使用浏览器自动复制，请手动复制下面内容", "chart_mode_cumulative": "累积", "chart_mode_incremental": "分时", "choosable": "可选", "chose_condition": "选择条件", "chosen": "已选", "classification": "分类", "click_drag_tip": "点我可拖动", "click_select_avatar": "点击选择头像", "click_to_copy": "点击复制", "click_to_resume": "点击恢复", "code_editor": "代码编辑", "code_error.account_error": "账号名或密码错误", "code_error.account_exist": "账号已注册", "code_error.account_not_found": "用户未注册", "code_error.app_error.invalid_app_type": "错误的应用类型", "code_error.app_error.invalid_owner": "非法的应用所有者", "code_error.app_error.not_exist": "应用不存在", "code_error.app_error.un_auth_app": "无权操作该应用", "code_error.chat_error.un_auth": "没有权限操作此对话记录", "code_error.error_code.400": "请求失败", "code_error.error_code.401": "无访问权限", "code_error.error_code.403": "紧张访问", "code_error.error_code.404": "请求不存在", "code_error.error_code.405": "请求方法错误", "code_error.error_code.406": "请求格式错误", "code_error.error_code.410": "资源已删除", "code_error.error_code.422": "验证错误", "code_error.error_code.429": "请求过于频繁", "code_error.error_code.500": "服务器发生错误", "code_error.error_code.502": "网关错误", "code_error.error_code.503": "服务器暂时过载或正在维护", "code_error.error_code.504": "网关超时", "code_error.error_message.403": "凭证错误", "code_error.error_message.510": "账户余额不足", "code_error.error_message.511": "没有权限操作此模型", "code_error.error_message.513": "没有权限读取该文件", "code_error.error_message.514": "Api Key 不合法", "code_error.openapi_error.api_key_not_exist": "Api Key 不存在", "code_error.openapi_error.exceed_limit": "最多 10 组 API 密钥", "code_error.openapi_error.un_auth": "无权操作该 Api Key", "code_error.outlink_error.invalid_link": "分享链接无效", "code_error.outlink_error.link_not_exist": "分享链接不存在", "code_error.outlink_error.un_auth_user": "身份校验失败", "code_error.plugin_error.not_exist": "工具不存在", "code_error.plugin_error.un_auth": "无权操作该工具", "code_error.system_error.community_version_num_limit": "超出开源版数量限制，请升级商业版: https://fastgpt.in", "code_error.system_error.license_app_amount_limit": "超出系统最大应用数量", "code_error.system_error.license_dataset_amount_limit": "超出系统最大知识库数量", "code_error.system_error.license_user_amount_limit": "超出系统最大用户数量", "code_error.team_error.ai_points_not_enough": "AI 积分不足", "code_error.team_error.app_amount_not_enough": "应用数量已达上限~", "code_error.team_error.cannot_delete_default_group": "不能删除默认角色", "code_error.team_error.cannot_delete_non_empty_org": "不能删除非空组织结构", "code_error.team_error.cannot_modify_root_org": "不能修改根组织结构", "code_error.team_error.cannot_move_to_sub_path": "不能移动到相同或子目录", "code_error.team_error.dataset_amount_not_enough": "知识库数量已达上限~", "code_error.team_error.dataset_size_not_enough": "知识库容量不足，请先扩容~", "code_error.team_error.group_name_duplicate": "角色名称重复", "code_error.team_error.group_name_empty": "角色名称不能为空", "code_error.team_error.group_not_exist": "角色不存在", "code_error.team_error.invitation_link_invalid": "邀请链接已失效", "code_error.team_error.not_user": "找不到该用户", "code_error.team_error.org_member_duplicated": "重复的组织结构用户", "code_error.team_error.org_member_not_exist": "组织结构用户不存在", "code_error.team_error.org_not_exist": "组织结构不存在", "code_error.team_error.org_parent_not_exist": "父组织结构不存在", "code_error.team_error.over_size": "团队用户超出限制", "code_error.team_error.plugin_amount_not_enough": "插件数量已达上限~", "code_error.team_error.re_rank_not_enough": "免费版无法使用检索重排~", "code_error.team_error.too_many_invitations": "您的有效邀请链接数已达上限，请先清理链接", "code_error.team_error.un_auth": "无权操作该团队", "code_error.team_error.user_not_active": "用户未接受或已离开团队", "code_error.team_error.website_sync_not_enough": "免费版无法使用Web站点同步~", "code_error.team_error.you_have_been_in_the_team": "你已经在该团队中", "code_error.token_error_code.403": "登录状态无效，请重新登录", "code_error.user_error.balance_not_enough": "账号余额不足~", "code_error.user_error.bin_visitor_guest": "您当前身份为游客，无权操作", "code_error.user_error.un_auth_user": "找不到该用户", "comfirm_import": "确认导入", "comfirm_leave_page": "确认离开该页面？", "comfirn_create": "确认创建", "commercial_function_tip": "请升级商业版后使用该功能：https://doc.fastgpt.cn/docs/commercial/intro/", "comon.Continue_Adding": "继续添加", "compliance.chat": "内容由第三方 AI 生成，无法确保真实准确，仅供参考", "compliance.dataset": "请确保您的内容严格遵守相关法律法规，避免包含任何违法或侵权的内容。请谨慎上传可能涉及敏感信息的资料。", "confirm_choice": "确认选择", "confirm_move": "移动到这", "confirm_update": "确认更新", "contact_way": "通知接收", "contribute_app_template": "贡献模板", "copy_successful": "复制成功", "copy_to_clipboard": "复制到剪贴板", "core.Chat": "对话", "core.ai.Max context": "最大上下文", "core.ai.Model": "AI 模型", "core.ai.Not deploy rerank model": "未部署重排模型", "core.ai.Prompt": "提示词", "core.ai.Support tool": "函数调用", "core.ai.model.Dataset Agent Model": "文本理解模型", "core.ai.model.Vector Model": "索引模型", "core.ai.model.doc_index_and_dialog": "文档索引 & 对话索引", "core.app.Api request": "API 访问", "core.app.Api request desc": "通过 API 接入到已有系统中，或企微、飞书等", "core.app.App intro": "应用介绍", "core.app.Auto execute": "自动执行", "core.app.Chat Variable": "对话框变量", "core.app.Config schedule plan": "配置定时执行", "core.app.Config whisper": "配置语音输入", "core.app.Config_auto_execute": "点击配置自动执行规则", "core.app.Interval timer config": "定时执行配置", "core.app.Interval timer run": "定时执行", "core.app.Interval timer tip": "可定时执行应用", "core.app.Make a brief introduction of your app": "给你的 AI 应用一个介绍", "core.app.Name and avatar": "头像 & 名称", "core.app.Publish": "发布", "core.app.Publish Confirm": "确认发布应用？会立即更新所有发布渠道的应用状态。", "core.app.Publish app tip": "发布应用后，所有发布渠道将会立即使用该版本", "core.app.QG.Custom prompt tip": "为保证生成的内容遵循正确格式，【黄色部分提示词】不允许修改", "core.app.QG.Custom prompt tip1": "为保证生成的内容遵循正确格式，", "core.app.QG.Custom prompt tip2": "【黄色部分提示词】", "core.app.QG.Custom prompt tip3": "不允许修改", "core.app.QG.Fixed Prompt": "请严格遵循格式规则：以 JSON 格式返回题目：\n['问题1'，'问题2'，'问题3']。", "core.app.Question Guide": "猜你想问", "core.app.Quote prompt": "引用模板提示词", "core.app.Quote templates": "引用内容模板", "core.app.Random": "发散", "core.app.Search team tags": "搜索标签", "core.app.Select TTS": "选择语音播放模式", "core.app.Select quote template": "选择引用提示模板", "core.app.Set a name for your app": "给应用设置一个名称", "core.app.Setting ai property": "点击配置 AI 模型相关属性", "core.app.Share link": "免登录窗口", "core.app.Share link desc": "分享链接给其他用户，无需登录即可直接进行使用", "core.app.Share link desc detail": "可以直接分享该模型给其他用户去进行对话，对方无需登录即可直接进行对话。注意，这个功能会消耗你账号的余额，请保管好链接！", "core.app.TTS": "语音播放", "core.app.TTS Tip": "开启后，每次对话后可使用语音播放功能。使用该功能可能产生额外费用。", "core.app.TTS start": "朗读内容", "core.app.Team tags": "团队标签", "core.app.Tool call": "工具调用", "core.app.ToolCall.No plugin": "没有可用的插件", "core.app.ToolCall.Parameter setting": "输入参数", "core.app.ToolCall.System": "系统", "core.app.ToolCall.Team": "团队", "core.app.Welcome Text": "对话开场白", "core.app.Whisper": "语音输入", "core.app.Whisper config": "语音输入配置", "core.app.deterministic": "严谨", "core.app.edit.Prompt Editor": "提示词编辑", "core.app.edit.Query extension background prompt": "对话背景描述", "core.app.edit.Query extension background tip": "描述当前对话的范围，便于 AI 为当前问题进行补全和扩展。填写的内容，通常为该助手所用", "core.app.edit_content": "应用信息编辑", "core.app.error.App name can not be empty": "应用名不能为空", "core.app.error.Get app failed": "获取应用异常", "core.app.feedback.Custom feedback": "自定义反馈", "core.app.feedback.close custom feedback": "关闭反馈", "core.app.have_saved": "已保存", "core.app.logs.Source And Time": "来源 & 时间", "core.app.more": "查看更多", "core.app.no_app": "还没有应用，快去创建一个吧！", "core.app.not_saved": "未保存", "core.app.outLink.Can Drag": "图标可拖拽", "core.app.outLink.Default open": "默认打开", "core.app.outLink.Iframe block title": "复制下面 iframe 加入到你的网站中", "core.app.outLink.Link block title": "将下面链接复制到浏览器打开", "core.app.outLink.Script Close Icon": "关闭图标", "core.app.outLink.Script Open Icon": "打开图标", "core.app.outLink.Script block title": "将下面代码加入到你的网站中", "core.app.outLink.Select Mode": "开始使用", "core.app.outLink.Select Using Way": "选择使用方式", "core.app.outLink.Show History": "展示历史对话", "core.app.publish.Fei shu bot": "飞书", "core.app.publish.Fei shu bot publish": "发布到飞书机器人", "core.app.schedule.Default prompt": "默认问题", "core.app.schedule.Default prompt placeholder": "执行应用时的默认问题", "core.app.schedule.Every day": "每天 {{hour}}:00", "core.app.schedule.Every month": "每月 {{day}} 号 {{hour}}:00", "core.app.schedule.Every week": "每周 {{day}} {{hour}}:00", "core.app.schedule.Interval": "每 {{interval}} 小时", "core.app.schedule.Open schedule": "定时执行", "core.app.setting": "应用信息设置", "core.app.share.Amount limit tip": "最多创建 10 组", "core.app.share.Create link": "创建新链接", "core.app.share.Create link tip": "创建成功。已复制分享地址，可直接分享使用", "core.app.share.Ip limit title": "IP 限流（人/分钟）", "core.app.share.Is response quote": "返回引用", "core.app.share.Not share link": "没有创建分享链接", "core.app.share.Role check": "身份校验", "core.app.switch_to_template_market": "跳转模板市场", "core.app.tip.Add a intro to app": "快来给应用一个介绍~", "core.app.tip.chatNodeSystemPromptTip": "在此输入提示词", "core.app.tip.systemPromptTip": "模型固定的引导词，通过调整该内容，可以引导模型聊天方向。该内容会被固定在上下文的开头。可通过输入 / 插入选择变量\n如果关联了知识库，你还可以通过适当的描述，来引导模型何时去调用知识库搜索。例如：\n你是电影《星际穿越》的助手，当用户询问与《星际穿越》相关的内容时，请搜索知识库并结合搜索结果进行回答。", "core.app.tip.variableTip": "可以在对话开始前，要求用户填写一些内容作为本轮对话的特定变量。该模块位于开场引导之后。\n输入框中，可通过 / 激活变量选择，例如：提示词、限定词等", "core.app.tip.welcomeTextTip": "每次对话开始前，发送一个初始内容。支持标准 Markdown 语法，可使用的额外标记：\n[快捷按键]：用户点击后可以直接发送该问题", "core.app.tool_label.doc": "使用文档", "core.app.tool_label.github": "GitHub地址", "core.app.tool_label.price": "计费说明", "core.app.tool_label.view_doc": "查看说明文档", "core.app.tts.Speech model": "语音模型", "core.app.tts.Speech speed": "语速", "core.app.tts.Test Listen": "试听", "core.app.tts.Test Listen Text": "你好，这是语音测试，如果你能听到这句话，说明语音播放功能正常", "core.app.whisper.Auto send": "自动发送", "core.app.whisper.Auto send tip": "语音输入完毕后直接发送，不需要再手动点击发送按键", "core.app.whisper.Auto tts response": "自动语音回复", "core.app.whisper.Auto tts response tip": "通过语音输入发送的问题，会直接以语音的形式响应，请确保打开了语音播报功能。", "core.app.whisper.Close": "关闭", "core.app.whisper.Not tts tip": "你没有开启语音播放，该功能无法使用", "core.app.whisper.Open": "开启", "core.app.whisper.Switch": "开启语音输入", "core.chat.Admin Mark Content": "纠正后的回复", "core.chat.Audio Not Support": "设备不支持语音播放", "core.chat.Audio Speech Error": "语音播报异常", "core.chat.Cancel Speak": "取消语音输入", "core.chat.Confirm to clear history": "确认清空该应用的在线聊天记录？分享和 API 调用的记录不会被清空。", "core.chat.Confirm to clear share chat history": "确认删除所有聊天记录？", "core.chat.Converting to text": "正在转换为文本...", "core.chat.Custom History Title": "自定义历史记录标题", "core.chat.Custom History Title Description": "如果设置为空，会自动跟随聊天记录。", "core.chat.Exit Chat": "退出聊天", "core.chat.Failed to initialize chat": "初始化聊天失败", "core.chat.Feedback Failed": "提交反馈异常", "core.chat.Feedback Modal": "结果反馈", "core.chat.Feedback Modal Tip": "输入你觉得回答不满意的地方", "core.chat.Feedback Submit": "提交反馈", "core.chat.Feedback Success": "反馈成功！", "core.chat.Finish Speak": "语音输入完成", "core.chat.History": "历史记录", "core.chat.History Amount": "{{amount}} 条记录", "core.chat.Mark": "标注预期回答", "core.chat.Mark Description": "当前标注功能为测试版。\n\n点击添加标注后，需要选择一个知识库，以便存储标注数据。你可以通过该功能快速的标注问题和预期回答，以便引导模型下次的回答。\n\n目前，标注功能同知识库其他数据一样，受模型的影响，不代表标注后 100% 符合预期。\n\n标注数据仅单向与知识库同步，如果知识库修改了该标注数据，日志展示的标注数据无法同步。", "core.chat.Mark Description Title": "标注功能介绍", "core.chat.New Chat": "新对话", "core.chat.Pin": "置顶", "core.chat.Question Guide": "猜你想问", "core.chat.Quote": "引用", "core.chat.Quote Amount": "知识库引用（{{amount}} 条）", "core.chat.Read Mark Description": "查看标注功能介绍", "core.chat.Recent use": "最近使用", "core.chat.Record": "语音输入", "core.chat.Restart": "重开对话", "core.chat.Run test": "运行预览", "core.chat.Select dataset": "选择知识库", "core.chat.Select dataset Desc": "选择一个知识库存储预期答案", "core.chat.Send Message": "发送", "core.chat.Speaking": "我在听，请说...", "core.chat.Start Chat": "开始对话", "core.chat.Type a message": "输入问题，发送 [Enter]/换行 [Ctrl(Alt/Shift) + Enter]", "core.chat.Unpin": "取消置顶", "core.chat.You need to a chat app": "你没有可用的应用", "core.chat.error.Chat error": "对话出现异常", "core.chat.error.Messages empty": "接口内容为空，可能文本超长了~", "core.chat.error.Select dataset empty": "你没有选择知识库", "core.chat.error.User input empty": "传入的用户问题为空", "core.chat.error.data_error": "获取数据异常", "core.chat.feedback.Close User Like": "用户表示赞同\n点击关闭该标记", "core.chat.feedback.Feedback Close": "关闭反馈", "core.chat.feedback.No Content": "用户没有填写具体反馈内容", "core.chat.feedback.Read User dislike": "用户表示反对\n点击查看内容", "core.chat.logs.api": "API 调用", "core.chat.logs.feishu": "飞书", "core.chat.logs.free_login": "免登录链接", "core.chat.logs.mcp": "MCP 调用", "core.chat.logs.official_account": "公众号", "core.chat.logs.online": "在线使用", "core.chat.logs.share": "外部链接调用", "core.chat.logs.team": "团队空间对话", "core.chat.logs.test": "在线调试", "core.chat.logs.wecom": "企业微信", "core.chat.markdown.Edit Question": "编辑问题", "core.chat.markdown.Quick Question": "点我立即提问", "core.chat.markdown.Send Question": "发送问题", "core.chat.module_unexist": "运行失败：应用缺失组件", "core.chat.quote.Quote Tip": "此处仅显示实际引用内容，若数据有更新，此处不会实时更新", "core.chat.quote.Read Quote": "查看引用", "core.chat.quote.afterUpdate": "更新后", "core.chat.quote.beforeUpdate": "更新前", "core.chat.response.Complete Response": "完整响应", "core.chat.response.Extension model": "问题优化模型", "core.chat.response.Read complete response": "查看详情", "core.chat.response.Read complete response tips": "点击查看详细流程", "core.chat.response.Tool call input tokens": "工具调用输入 Tokens", "core.chat.response.Tool call output tokens": "工具调用输出 Tokens", "core.chat.response.Tool call tokens": "工具调用 tokens 消耗", "core.chat.response.context total length": "上下文总长度", "core.chat.response.loop_input": "输入数组", "core.chat.response.loop_input_element": "输入数组元素", "core.chat.response.loop_output": "输出数组", "core.chat.response.loop_output_element": "输出数组元素", "core.chat.response.module cq": "问题分类列表", "core.chat.response.module cq result": "分类结果", "core.chat.response.module extract description": "提取背景描述", "core.chat.response.module extract result": "提取结果", "core.chat.response.module historyPreview": "记录预览(仅展示部分内容)", "core.chat.response.module http result": "响应体", "core.chat.response.module if else Result": "判断器结果", "core.chat.response.module limit": "单次搜索上限", "core.chat.response.module maxToken": "最大响应 tokens", "core.chat.response.module model": "模型", "core.chat.response.module name": "模型名", "core.chat.response.module query": "问题/检索词", "core.chat.response.module similarity": "相似度", "core.chat.response.module temperature": "温度", "core.chat.response.module time": "运行时长", "core.chat.response.plugin output": "插件输出值", "core.chat.response.search using reRank": "结果重排", "core.chat.response.text output": "文本输出", "core.chat.response.update_var_result": "变量更新结果(按顺序展示多个变量更新结果)", "core.chat.response.user_select_result": "用户选择结果", "core.chat.retry": "重新生成", "core.chat.tts.Stop Speech": "停止", "core.dataset.Choose Dataset": "关联知识库", "core.dataset.Collection": "数据集", "core.dataset.Create dataset": "创建一个{{name}}", "core.dataset.Dataset": "知识库", "core.dataset.Dataset ID": "知识库 ID", "core.dataset.Delete Confirm": "确认删除该知识库？删除后数据无法恢复，请确认！", "core.dataset.Empty Dataset": "空数据集", "core.dataset.Empty Dataset Tips": "还没有知识库，快去创建一个吧！", "core.dataset.Folder placeholder": "这是一个目录", "core.dataset.Intro Placeholder": "这个知识库还没有介绍~", "core.dataset.My Dataset": "我的知识库", "core.dataset.Query extension intro": "开启问题优化功能，可以提高提高连续对话时，知识库搜索的精度。开启该功能后，在进行知识库搜索时，会根据对话记录，利用 AI 补全问题缺失的信息。", "core.dataset.Quote Length": "引用内容长度", "core.dataset.Read Dataset": "查看知识库详情", "core.dataset.Set Website Config": "开始配置", "core.dataset.Start export": "已开始导出", "core.dataset.Text collection": "文本数据集", "core.dataset.apiFile": "API 文件", "core.dataset.collection.Click top config website": "点击配置网站", "core.dataset.collection.Collection raw text": "数据集内容", "core.dataset.collection.Empty Tip": "数据集空空如也", "core.dataset.collection.QA Prompt": "QA 拆分引导词", "core.dataset.collection.Start Sync Tip": "确认开始同步数据？将会删除旧数据后重新获取，请确认！", "core.dataset.collection.Sync": "同步数据", "core.dataset.collection.Sync Collection": "数据同步", "core.dataset.collection.Website Empty Tip": "还没有关联网站", "core.dataset.collection.Website Link": "Web 站点地址", "core.dataset.collection.id": "集合 ID", "core.dataset.collection.metadata.Createtime": "创建时间", "core.dataset.collection.metadata.Raw text length": "原文长度", "core.dataset.collection.metadata.Updatetime": "更新时间", "core.dataset.collection.metadata.Web page selector": "网站选择器", "core.dataset.collection.metadata.metadata": "元数据", "core.dataset.collection.metadata.read source": "查看原始内容", "core.dataset.collection.metadata.source": "数据来源", "core.dataset.collection.metadata.source size": "来源大小", "core.dataset.collection.status.active": "已就绪", "core.dataset.collection.status.error": "训练异常", "core.dataset.collection.sync.result.sameRaw": "内容未变动，无需更新", "core.dataset.collection.sync.result.success": "开始同步", "core.dataset.data.Data Content": "相关数据内容", "core.dataset.data.Default Index Tip": "无法编辑，默认索引会使用【相关数据内容】与【辅助数据】的文本直接生成索引。", "core.dataset.data.Edit": "编辑数据", "core.dataset.data.Empty Tip": "这个集合还没有数据~", "core.dataset.data.Search data placeholder": "搜索相关数据", "core.dataset.data.Too Long": "总长度超长了", "core.dataset.data.Updated": "已更新", "core.dataset.data.group": "组", "core.dataset.data.unit": "条", "core.dataset.embedding model tip": "索引模型可以将自然语言转成向量，用于进行语义检索。\n注意，不同索引模型无法一起使用，选择完索引模型后将无法修改。", "core.dataset.error.Data not found": "数据不存在或已被删除", "core.dataset.error.Start Sync Failed": "开始同步失败", "core.dataset.error.invalidVectorModelOrQAModel": "VectorModel 或 QA 模型错误", "core.dataset.error.unAuthDataset": "无权操作该知识库", "core.dataset.error.unAuthDatasetCollection": "无权操作该数据集", "core.dataset.error.unAuthDatasetData": "无权操作该数据", "core.dataset.error.unAuthDatasetFile": "无权操作该文件", "core.dataset.error.unCreateCollection": "无权操作该数据", "core.dataset.error.unExistDataset": "知识库不存在", "core.dataset.error.unLinkCollection": "不是网络链接集合", "core.dataset.externalFile": "外部文件库", "core.dataset.file": "文件", "core.dataset.folder": "目录", "core.dataset.import.Chunk Range": "范围：{{min}}~{{max}}", "core.dataset.import.Chunk Split Tip": "将文本按一定的规则进行分段处理后，转成可进行语义搜索的格式，适合绝大多数场景。不需要调用模型额外处理，成本低。", "core.dataset.import.Continue upload": "继续上传", "core.dataset.import.Custom prompt": "自定义提示词", "core.dataset.import.Custom text": "自定义文本", "core.dataset.import.Custom text desc": "手动输入一段文本作为数据集", "core.dataset.import.Data process params": "数据处理参数", "core.dataset.import.Down load csv template": "点击下载 CSV 模板", "core.dataset.import.Link name": "网络链接", "core.dataset.import.Link name placeholder": "仅支持静态链接，如果上传后数据为空，可能该链接无法被读取\n每行一个，每次最多 10 个链接", "core.dataset.import.Local file": "本地文件", "core.dataset.import.Local file desc": "上传 PDF、TXT、DOCX 等格式的文件", "core.dataset.import.Preview chunks": "预览分段（最多 15 段）", "core.dataset.import.Preview raw text": "预览源文本（最多 3000 字）", "core.dataset.import.Process way": "处理方式", "core.dataset.import.QA Import": "QA 拆分", "core.dataset.import.QA Import Tip": "根据一定规则，将文本拆成一段较大的段落，调用 AI 为该段落生成问答对。有非常高的检索精度，但是会丢失很多内容细节。", "core.dataset.import.Select file": "选择文件", "core.dataset.import.Select source": "选择来源", "core.dataset.import.Source name": "来源名", "core.dataset.import.Sources list": "来源列表", "core.dataset.import.Start upload": "开始上传", "core.dataset.import.Upload complete": "完成上传", "core.dataset.import.Upload data": "确认上传", "core.dataset.import.Upload file progress": "文件上传进度", "core.dataset.import.Upload status": "状态", "core.dataset.import.Web link": "网页链接", "core.dataset.import.Web link desc": "读取静态网页内容作为数据集", "core.dataset.import.import_success": "导入成功，请等待训练", "core.dataset.link": "链接", "core.dataset.search.Dataset Search Params": "知识库搜索配置", "core.dataset.search.Empty result response": "空搜索回复", "core.dataset.search.Filter": "搜索过滤", "core.dataset.search.No support similarity": "仅使用结果重排或语义检索时，支持相关度过滤", "core.dataset.search.Nonsupport": "不支持", "core.dataset.search.Params Setting": "搜索参数设置", "core.dataset.search.Quote index": "第几个引用", "core.dataset.search.ReRank": "结果重排", "core.dataset.search.ReRank desc": "使用重排模型来进行二次排序，可增强综合排名。", "core.dataset.search.Source id": "来源 ID", "core.dataset.search.Source index": "第几个来源", "core.dataset.search.Source name": "引用来源名", "core.dataset.search.Using query extension": "使用问题优化", "core.dataset.search.mode.embedding": "语义检索", "core.dataset.search.mode.embedding desc": "使用向量进行文本相关性查询", "core.dataset.search.mode.fullTextRecall": "全文检索", "core.dataset.search.mode.fullTextRecall desc": "使用传统的全文检索，适合查找一些关键词和主谓语特殊的数据", "core.dataset.search.mode.mixedRecall": "混合检索", "core.dataset.search.mode.mixedRecall desc": "使用向量检索与全文检索的综合结果返回，使用 RRF 算法进行排序。", "core.dataset.search.score.embedding desc": "通过计算向量之间的距离获取得分，范围为 0~1。", "core.dataset.search.score.fullText": "全文检索", "core.dataset.search.score.fullText desc": "计算相同关键词的得分，范围为 0~无穷。", "core.dataset.search.score.reRank": "结果重排", "core.dataset.search.score.reRank desc": "通过 Rerank 模型计算句子之间的关联度，范围为 0~1。", "core.dataset.search.score.rrf": "综合排名", "core.dataset.search.score.rrf desc": "通过倒排计算的方式，合并多个检索结果。", "core.dataset.search.search mode": "搜索方式", "core.dataset.status.active": "已就绪", "core.dataset.status.syncing": "同步中", "core.dataset.status.waiting": "排队中", "core.dataset.test.Batch test": "批量测试", "core.dataset.test.Batch test Placeholder": "选择一个 CSV 文件", "core.dataset.test.Search Test": "搜索测试", "core.dataset.test.Test": "测试", "core.dataset.test.Test Result": "测试结果", "core.dataset.test.Test Text": "单个文本测试", "core.dataset.test.Test Text Placeholder": "输入需要测试的文本", "core.dataset.test.Test params": "测试参数", "core.dataset.test.delete test history": "删除该测试结果", "core.dataset.test.test history": "测试历史", "core.dataset.test.test result placeholder": "测试结果将在这里展示", "core.dataset.test.test result tip": "根据知识库内容与测试文本的相似度进行排序，你可以根据测试结果调整对应的文本。\n注意：测试记录中的数据可能已经被修改过，点击某条测试数据后将展示最新的数据。", "core.dataset.training.Agent queue": "QA 训练排队", "core.dataset.training.Auto mode": "补充索引", "core.dataset.training.Auto mode Tip": "通过子索引以及调用模型生成相关问题与摘要，来增加数据块的语义丰富度，更利于检索。需要消耗更多的存储空间和增加 AI 调用次数。", "core.dataset.training.Chunk mode": "分块存储", "core.dataset.training.Full": "预计 20 分钟以上", "core.dataset.training.Leisure": "空闲", "core.dataset.training.QA mode": "问答对提取", "core.dataset.training.Vector queue": "索引排队", "core.dataset.training.Waiting": "预计 20 分钟", "core.dataset.training.Website Sync": "Web 站点同步", "core.dataset.training.tag": "排队情况", "core.dataset.website.Base Url": "根地址", "core.dataset.website.Config": "Web 站点配置", "core.dataset.website.Config Description": "Web 站点同步功能允许你填写一个网站的根地址，系统会自动深度抓取相关的网页进行知识库训练。仅会抓取静态的网站，以项目文档、博客为主。", "core.dataset.website.Confirm Create Tips": "确认同步该站点，同步任务将随后开启，请确认！", "core.dataset.website.Confirm Update Tips": "确认更新站点配置？会立即按新的配置开始同步，请确认！", "core.dataset.website.Selector": "选择器", "core.dataset.website.Selector Course": "使用教程", "core.dataset.website.Start Sync": "开始同步", "core.dataset.website.UnValid Website Tip": "您的站点可能非静态站点，无法同步", "core.module.Add question type": "添加问题类型", "core.module.Add_option": "添加选项", "core.module.Can not connect self": "不能连接自身", "core.module.Data Type": "数据类型", "core.module.Dataset quote.label": "知识库引用", "core.module.Dataset quote.select": "选择知识库引用", "core.module.Default Value": "默认值", "core.module.Default value": "默认值", "core.module.Default value placeholder": "不填则默认返回空字符", "core.module.Diagram": "示意图", "core.module.Edit intro": "编辑描述", "core.module.Field Description": "字段描述", "core.module.Field Name": "字段名", "core.module.Http request props": "请求参数", "core.module.Http request settings": "请求配置", "core.module.Http timeout": "超时时长", "core.module.Input Type": "输入类型", "core.module.Laf sync params": "同步参数", "core.module.Max Length": "最大长度", "core.module.Max Length placeholder": "输入文本的最大长度", "core.module.Max Value": "最大值", "core.module.Min Value": "最小值", "core.module.QueryExtension.placeholder": "例如：\n关于 Python 的介绍和使用等问题。\n当前对话与游戏《GTA5》有关。", "core.module.Select app": "选择应用", "core.module.Setting quote prompt": "配置引用提示词", "core.module.Variable": "全局变量", "core.module.Variable Setting": "变量设置", "core.module.edit.Field Name Cannot Be Empty": "字段名不能为空", "core.module.edit.Field Value Type Cannot Be Empty": "可选数据类型不能为空", "core.module.extract.Add field": "新增字段", "core.module.extract.Enum Description": "列举出该字段可能的值，每行一个", "core.module.extract.Enum Value": "枚举值", "core.module.extract.Field Description Placeholder": "姓名/年龄/SQL 语句……", "core.module.extract.Field Setting Title": "提取字段配置", "core.module.extract.Required": "必须返回", "core.module.extract.Required Description": "即使无法提取该字段，也会使用默认值进行返回", "core.module.extract.Target field": "目标字段", "core.module.http.Add props": "添加参数", "core.module.http.AppId": "应用 ID", "core.module.http.ChatId": "当前对话 ID", "core.module.http.Current time": "当前时间", "core.module.http.Histories": "历史记录", "core.module.http.Key already exists": "Key 已经存在", "core.module.http.Key cannot be empty": "参数名不能为空", "core.module.http.Props name": "参数名", "core.module.http.Props tip": "可以设置 HTTP 请求的相关参数\n可通过输入 / 来调用变量，当前可使用变量：\n{{variable}}", "core.module.http.Props value": "参数值", "core.module.http.ResponseChatItemId": "AI 回复的 ID", "core.module.http.Url and params have been split": "路径参数已被自动加入 Params 中", "core.module.http.curl import": "cURL 导入", "core.module.http.curl import placeholder": "请输入 cURL 格式内容，将会提取第一个接口的请求信息。", "core.module.input.Add Branch": "添加分支", "core.module.input.add": "添加条件", "core.module.input.description.Background": "你可以添加一些特定内容的介绍，从而更好的识别用户的问题类型。这个内容通常是给模型介绍一个它不知道的内容。", "core.module.input.description.HTTP Dynamic Input": "接收前方节点的输出值作为变量，这些变量可以被 HTTP 请求参数使用。", "core.module.input.description.Http Request Header": "自定义请求头，请严格填入 JSON 字符串。\n1. 确保最后一个属性没有逗号\n2. 确保 key 包含双引号\n例如：{\"Authorization\":\"Bearer xxx\"}", "core.module.input.description.Http Request Url": "新的 HTTP 请求地址。如果出现两个“请求地址”，可以删除该模块重新加入，会拉取最新的模块配置。", "core.module.input.description.Response content": "可以使用 \\n 来实现连续换行。\n可以通过外部模块输入实现回复，外部模块输入时会覆盖当前填写的内容。\n如传入非字符串类型数据将会自动转成字符串", "core.module.input.label.Background": "背景知识", "core.module.input.label.Http Request Url": "请求地址", "core.module.input.label.Response content": "回复的内容", "core.module.input.label.Select dataset": "选择知识库", "core.module.input.label.aiModel": "AI 模型", "core.module.input.label.chat history": "聊天记录", "core.module.input.label.user question": "用户问题", "core.module.input.placeholder.Classify background": "例如：\n1. AIGC（人工智能生成内容）是指使用人工智能技术自动或半自动地生成数字内容，如文本、图像、音乐、视频等。\n2. AIGC 技术包括但不限于自然语言处理、计算机视觉、机器学习和深度学习。这些技术可以创建新内容或修改现有内容，以满足特定的创意、教育、娱乐或信息需求。", "core.module.input_description": "输入描述", "core.module.input_form": "输入字段", "core.module.input_name": "输入名", "core.module.input_type": "输入类型", "core.module.laf.Select laf function": "选择 laf 函数", "core.module.output.description.Ai response content": "将在 stream 回复完毕后触发", "core.module.output.description.New context": "将本次回复内容拼接上历史记录，作为新的上下文返回", "core.module.output.description.query extension result": "以字符串数组的形式输出，可将该结果直接连接到“知识库搜索”的“用户问题”中，建议不要连接到“AI 对话”的“用户问题”中", "core.module.output.label.Ai response content": "AI 回复内容", "core.module.output.label.New context": "新的上下文", "core.module.output.label.query extension result": "优化结果", "core.module.template.AI function": "AI能力", "core.module.template.AI response switch tip": "如果你希望当前节点不输出内容，可以关闭该开关。AI 输出的内容不会展示给用户，你可以手动的使用“AI 回复内容”进行特殊处理。", "core.module.template.AI support tool tip": "支持函数调用的模型，可以更好的使用工具调用。", "core.module.template.Basic Node": "基础功能", "core.module.template.Query extension": "问题优化", "core.module.template.System Plugin": "系统插件", "core.module.template.System input module": "系统输入", "core.module.template.Team app": "团队应用", "core.module.template.Tool module": "工具", "core.module.template.UnKnow Module": "未知模块", "core.module.template.ai_chat": "AI 对话", "core.module.template.ai_chat_intro": "AI 大模型对话", "core.module.template.config_params": "可以配置应用的系统参数", "core.module.template.empty_plugin": "空白插件", "core.module.template.empty_workflow": "空白工作流", "core.module.template.self_input": "插件输入", "core.module.template.self_output": "插件输出", "core.module.template.system_config": "系统配置", "core.module.template.system_config_info": "可以配置应用的系统参数", "core.module.template.work_start": "流程开始", "core.module.templates.Load plugin error": "加载插件失败", "core.module.variable add option": "添加选项", "core.module.variable.Custom type": "自定义变量", "core.module.variable.add option": "添加选项", "core.module.variable.input type": "文本", "core.module.variable.key": "变量 key", "core.module.variable.key already exists": "Key 已经存在", "core.module.variable.key is required": "变量 key 是必须的", "core.module.variable.select type": "下拉单选", "core.module.variable.text max length": "最大长度", "core.module.variable.textarea type": "段落", "core.module.variable.variable name is required": "变量名不能为空", "core.module.variable.variable option is required": "选项不能全空", "core.module.variable.variable option is value is required": "选项内容不能为空", "core.module.variable.variable options": "选项", "core.plugin.Custom headers": "自定义请求头", "core.plugin.Free": "该插件无需积分消耗～", "core.plugin.Get Plugin Module Detail Failed": "加载插件异常", "core.plugin.Http plugin intro placeholder": "仅做展示，无实际效果", "core.plugin.cost": "积分消耗：", "core.tip.leave page": "内容已修改，确认离开页面吗？", "core.view_chat_detail": "查看对话详情", "core.workflow.Can not delete node": "该节点不允许删除", "core.workflow.Change input type tip": "修改输入类型会清空已填写的值，请确认！", "core.workflow.Check Failed": "工作流校验失败，请检查是否缺失、缺值，连线是否正常", "core.workflow.Confirm stop debug": "确认终止调试？调试信息将会不保留。", "core.workflow.Copy node": "已复制节点", "core.workflow.Custom inputs": "自定义输入", "core.workflow.Custom outputs": "自定义输出", "core.workflow.Dataset quote": "知识库引用", "core.workflow.Debug": "调试", "core.workflow.Debug Node": "Debug 模式", "core.workflow.Failed": "运行失败", "core.workflow.Not intro": "这个节点没有介绍~", "core.workflow.Run": "运行", "core.workflow.Running": "运行中", "core.workflow.Save and publish": "保存并发布", "core.workflow.Save to cloud": "仅保存", "core.workflow.Skipped": "跳过运行", "core.workflow.Stop debug": "停止调试", "core.workflow.Success": "运行成功", "core.workflow.Value type": "数据类型", "core.workflow.debug.Done": "完成调试", "core.workflow.debug.Hide result": "隐藏结果", "core.workflow.debug.Not result": "无运行结果", "core.workflow.debug.Run result": "运行结果", "core.workflow.debug.Show result": "展示结果", "core.workflow.dynamic_input": "动态输入", "core.workflow.inputType.JSON Editor": "JSON 输入框", "core.workflow.inputType.Manual input": "手动输入", "core.workflow.inputType.Manual select": "手动选择", "core.workflow.inputType.Reference": "变量引用", "core.workflow.inputType.custom": "自定义变量", "core.workflow.inputType.dynamicTargetInput": "动态外部数据", "core.workflow.inputType.input": "单行输入框", "core.workflow.inputType.number input": "数字输入框", "core.workflow.inputType.select": "单选框", "core.workflow.inputType.selectApp": "应用选择", "core.workflow.inputType.selectDataset": "知识库选择", "core.workflow.inputType.selectLLMModel": "对话模型选择", "core.workflow.inputType.switch": "开关", "core.workflow.inputType.textInput": "文本输入框", "core.workflow.inputType.textarea": "多行输入框", "core.workflow.publish.OnRevert version": "点击回退到该版本", "core.workflow.publish.OnRevert version confirm": "确认回退至该版本？会为您保存编辑中版本的配置，并为回退版本创建一个新的发布版本。", "core.workflow.publish.histories": "发布记录", "core.workflow.template.Interactive": "交互", "core.workflow.template.Multimodal": "多模态", "core.workflow.template.Search": "搜索", "core.workflow.tool.Handle": "工具连接器", "core.workflow.tool.Select Tool": "选择工具", "core.workflow.variable": "变量", "create": "去创建", "create_failed": "创建失败", "create_success": "创建成功", "create_time": "创建时间", "cron_job_run_app": "定时任务", "custom_title": "自定义标题", "data_index_custom": "自定义索引", "data_index_default": "默认索引", "data_index_question": "推测问题索引", "data_index_summary": "摘要索引", "data_not_found": "数据找不到了", "dataset.Confirm move the folder": "确认移动到该目录", "dataset.Confirm to delete the data": "确认删除该数据？", "dataset.Confirm to delete the file": "确认删除该文件及其所有数据？", "dataset.Create Folder": "创建文件夹", "dataset.Create manual collection": "创建手动数据集", "dataset.Delete Dataset Error": "删除知识库异常", "dataset.Edit Folder": "编辑文件夹", "dataset.Edit Info": "编辑信息", "dataset.Export": "导出", "dataset.Export Dataset Limit Error": "导出数据失败", "dataset.Folder Name": "输入文件夹名称", "dataset.Insert Data": "插入", "dataset.Manual collection Tip": "手动数据集允许创建一个空的容器装入数据", "dataset.Move Failed": "移动出现错误~", "dataset.Select Dataset": "选择该知识库", "dataset.Select Dataset Tips": "仅能选择同一个索引模型的知识库", "dataset.Select Folder": "进入文件夹", "dataset.Training Name": "数据训练", "dataset.collections.Collection Embedding": "{{total}} 组索引中", "dataset.collections.Confirm to delete the folder": "确认删除该文件夹及里面所有内容？", "dataset.collections.Create And Import": "新建/导入", "dataset.collections.Select Collection": "选择文件", "dataset.collections.Select One Collection To Store": "选择一个文件进行存储", "dataset.data.Can not edit": "无编辑权限", "dataset.data.Default Index": "默认索引", "dataset.data.Delete Tip": "确认删除该条数据？", "dataset.data.Index Placeholder": "输入索引文本内容", "dataset.data.Input Success Tip": "导入数据成功", "dataset.data.Update Success Tip": "更新数据成功", "dataset.data.edit.Index": "数据索引({{amount}})", "dataset.data.edit.divide_content": "分块内容", "dataset.data.input is empty": "数据内容不能为空 ", "dataset.dataset_name": "知识库名称", "dataset.deleteFolderTips": "确认删除该文件夹及其包含的所有知识库？删除后数据无法恢复，请确认！", "dataset.test.noResult": "搜索结果为空", "dataset_data_input_a": "答案", "dataset_data_input_chunk": "常规模式", "dataset_data_input_chunk_content": "内容", "dataset_data_input_q": "问题", "dataset_data_input_qa": "QA 模式", "dataset_text_model_tip": "用于知识库预处理阶段的文本处理，例如自动补充索引、问答对提取。", "deep_rag_search": "深度搜索", "delete_api": "确认删除该API密钥？删除后该密钥立即失效，对应的对话日志不会删除，请确认！", "delete_failed": "删除失败", "delete_folder": "删除文件夹", "delete_success": "删除成功", "delete_warning": "删除警告", "embedding_model_not_config": "检测到没有可用的索引模型", "enable_auth": "启用鉴权", "error.Create failed": "创建失败", "error.code_error": "验证码错误", "error.fileNotFound": "文件找不到了~", "error.inheritPermissionError": "权限继承错误", "error.invalid_params": "参数无效", "error.missingParams": "参数缺失", "error.send_auth_code_too_frequently": "请勿频繁获取验证码", "error.too_many_request": "请求太频繁了，请稍后重试", "error.unKnow": "出现了点意外~", "error.upload_file_error_filename": "{{name}} 上传失败", "error.upload_image_error": "上传文件失败", "error.username_empty": "账号不能为空", "error_collection_not_exist": "集合不存在", "error_embedding_not_config": "未配置索引模型", "error_invalid_resource": "无效的资源", "error_llm_not_config": "未配置文件理解模型", "error_un_permission": "无权操作", "error_vlm_not_config": "未配置图片理解模型", "exit_directly": "直接退出", "expired_time": "过期时间", "export_to_json": "导出为 JSON", "extraction_results": "提取结果", "failed": "失败", "field_name": "字段名", "folder.empty": "这个目录已经没东西可选了~", "folder.open_dataset": "打开知识库", "folder_description": "文件夹描述", "free": "免费", "get_QR_failed": "获取二维码失败", "get_app_failed": "获取应用失败", "get_laf_failed": "获取Laf函数列表失败", "had_auth_value": "已填写", "has_verification": "已验证，点击取消绑定", "have_done": "已完成", "import_failed": "导入失败", "import_success": "导入成功", "info.buy_extra": "购买额外套餐", "info.csv_download": "点击下载批量测试模板", "info.csv_message": "读取 CSV 文件第一列进行批量测试，单次最多支持 100 组数据。", "info.felid_message": "字段key必须是纯英文字母或数字，并且不能以数字开头。", "info.free_plan": "免费版团队连续30天未登录系统时，系统会自动清理账号知识库。", "info.include": "包含标准套餐与额外资源包", "info.node_info": "调整该模块会对工具调用时机有影响。\n你可以通过精确的描述该模块功能，引导模型进行工具调用。", "info.old_version_attention": "检测到您的高级编排为旧版，系统将为您自动格式化成新版工作流。\n\n由于版本差异较大，会导致一些工作流无法正常排布，请重新手动连接工作流。如仍异常，可尝试删除对应节点后重新添加。\n\n你可以直接点击调试进行工作流测试，调试完毕后点击发布。直到你点击发布，新工作流才会真正保存生效。\n\n在你发布新工作流前，自动保存不会生效。", "info.open_api_notice": "可以填写 OpenAI/OneAPI的相关密钥。如果你填写了该内容，在线上平台使用【AI对话】、【问题分类】和【内容提取】将会走你填写的Key，不会计费。请注意你的Key 是否有访问对应模型的权限。GPT模型可以选择 FastAI。", "info.open_api_placeholder": "请求地址，默认为 openai 官方。可填中转地址，未自动补全 \"v1\"", "info.resource": "资源用量", "input.Repeat Value": "有重复的值", "input_name": "取个名字", "invalid_variable": "无效变量", "is_open": "是否开启", "is_requesting": "请求中……", "is_using": "正在使用", "item_description": "字段描述", "item_name": "字段名", "json_config": "JSON 配置", "json_parse_error": "JSON 可能有误，请仔细检查", "just_now": "刚刚", "key": "键", "key_repetition": "key 重复", "last_step": "上一步", "last_use_time": "最后使用时间", "link.UnValid": "无效的链接", "llm_model_not_config": "检测到没有可用的语言模型", "load_failed": "加载失败", "max_quote_tokens": "引用上限", "max_quote_tokens_tips": "单次搜索最大的 token 数量，中文约 1 字=1.7 tokens，英文约 1 字=1 token", "mcp_server": "MCP 服务", "min_similarity": "最低相关度", "min_similarity_tip": "不同索引模型的相关度有区别，请通过搜索测试来选择合适的数值。使用 结果重排 时，使用重排结果进行过滤。", "model.billing": "模型计费", "model.model_type": "模型类型", "model.name": "模型名", "model.provider": "模型提供商", "model.search_name_placeholder": "根据模型名搜索", "model.type.chat": "语言模型", "model.type.embedding": "索引模型", "model.type.reRank": "重排模型", "model.type.stt": "语音识别", "model.type.tts": "语音合成", "model_alicloud": "阿里云", "model_baai": "智源", "model_baichuan": "百川智能", "model_chatglm": "ChatGLM", "model_doubao": "豆包", "model_ernie": "文心一言", "model_hunyuan": "腾讯混元", "model_intern": "书生", "model_moka": "Moka-AI", "model_moonshot": "月之暗面", "model_other": "其他", "model_ppio": "PPIO 派欧云", "model_qwen": "阿里千问", "model_siliconflow": "硅基流动", "model_sparkdesk": "讯飞星火", "model_stepfun": "阶跃星辰", "model_yi": "零一万物", "month": "月", "move.confirm": "确认移动", "move_success": "移动成功", "move_to": "移动到", "name": "名称", "name_is_empty": "名称不能为空", "navbar.Account": "账号", "navbar.Chat": "聊天", "navbar.Datasets": "知识库", "navbar.Studio": "工作台", "navbar.Toolkit": "工具箱", "navbar.Tools": "工具", "new_create": "新建", "next_step": "下一步", "no": "否", "no_child_folder": "没有子目录了，就放这里吧", "no_intro": "暂无介绍", "no_laf_env": "系统未配置Laf环境", "no_more_data": "没有更多了~", "no_pay_way": "系统无合适的支付渠道", "no_select_data": "没有可选值", "not_model_config": "未配置相关模型", "not_open": "未开启", "not_permission": "当前订阅套餐不支持团队操作日志", "not_support": "不支持", "not_support_wechat_image": "这是一张微信图片", "not_yet_introduced": "暂无介绍", "open_folder": "打开文件夹", "option": "选项", "page_center": "页面居中", "pay.amount": "金额", "pay.error_desc": "转换支付途径时出现了问题", "pay.noclose": "支付完成后，请等待系统自动更新", "pay.package_tip.buy": "您购买的套餐等级低于当前套餐，该套餐将在当前套餐过期后生效。\n您可在账号—个人信息—套餐详情里，查看套餐使用情况。", "pay.package_tip.renewal": "您正在续费套餐。您可在账号—个人信息—套餐详情里，查看套餐使用情况。", "pay.package_tip.upgrade": "您购买的套餐等级高于当前套餐，该套餐将即刻生效，当前套餐将延后生效。您可在账号—个人信息—套餐详情里，查看套餐使用情况。", "pay.wechat": "请微信扫码支付: {{price}}元\n支付完成前，请勿关闭页面", "pay.wx_payment": "微信支付", "pay.yuan": "{{amount}}元", "pay_alipay_payment": "支付宝支付", "pay_corporate_payment": "对公支付", "pay_money": "应付金额", "pay_success": "支付成功", "pay_year_tip": "支付 10 个月，畅享 1 年！", "permission.Collaborator": "协作者", "permission.Default permission": "默认权限", "permission.Manage": "管理", "permission.No InheritPermission": "已限制权限，不再继承父级文件夹的权限，", "permission.Not collaborator": "暂无协作者", "permission.Owner": "创建者", "permission.Permission": "权限", "permission.Permission config": "权限配置", "permission.Private": "私有", "permission.Private Tip": "仅自己可用", "permission.Public": "协作", "permission.Public Tip": "团队所有用户可使用", "permission.Remove InheritPermission Confirm": "此操作会导致权限继承失效，是否进行？", "permission.Resume InheritPermission Confirm": "是否恢复为继承父级文件夹的权限？", "permission.Resume InheritPermission Failed": "恢复失败", "permission.Resume InheritPermission Success": "恢复成功", "permission.change_owner": "转移所有权", "permission.change_owner_failed": "转移所有权失败", "permission.change_owner_placeholder": "输入用户名查找账号", "permission.change_owner_success": "成功转移所有权", "permission.change_owner_tip": "转移后您的权限不会保留", "permission.change_owner_to": "转移给", "permission.manager": "管理员", "permission.read": "读权限", "permission.write": "写权限", "please_input_name": "请输入名称", "plugin.App": "选择应用", "plugin.Currentapp": "当前应用", "plugin.Description": "描述", "plugin.Edit Http Plugin": "编辑 HTTP 插件", "plugin.Enter PAT": "请输入访问凭证（PAT）", "plugin.Get Plugin Module Detail Failed": "获取插件信息异常", "plugin.Import Plugin": "导入 HTTP 插件", "plugin.Import from URL": "从 URL 导入。https://xxxx", "plugin.Intro": "插件介绍", "plugin.Invalid Env": "laf 环境错误", "plugin.Invalid Schema": "Schema 无效", "plugin.Invalid URL": "URL 无效", "plugin.Method": "方法", "plugin.Path": "路径", "plugin.Please bind laf accout first": "请先绑定 laf 账号", "plugin.Plugin List": "插件列表", "plugin.Search plugin": "搜索插件", "plugin.Search_app": "搜索应用", "plugin.Set Name": "给插件取个名字", "plugin.contribute": "贡献插件", "plugin.go to laf": "去编写", "plugin.path": "路径", "price_over_wx_limit": "超出支付提供商限额：微信支付仅支持 6000 元以下", "prompt_input_placeholder": "请输入提示词", "psw_inconsistency": "两次密码不一致", "question_feedback": "工单咨询", "read_course": "查看教程", "read_doc": "查看文档", "read_quote": "查看引用", "redo_tip": "恢复  ctrl  shift  z", "redo_tip_mac": "恢复  ⌘  shift  z", "request_end": "已加载全部", "request_error": "请求异常", "request_more": "点击加载更多", "required": "必须", "rerank_weight": "重排权重", "resume_failed": "恢复失败", "root_folder": "根目录", "save_failed": "保存异常", "save_success": "保存成功", "scan_code": "扫码支付", "secret_tips": "值保存后不会再次明文返回", "select_file_failed": "选择文件异常", "select_reference_variable": "选择引用变量", "select_template": "选择模板", "set_avatar": "点击设置头像", "share_link": "分享链接", "speech_error_tip": "语音转文字失败", "speech_not_support": "您的浏览器不支持语音输入", "submit_failed": "提交失败", "submit_success": "提交成功", "submitted": "已提交", "support": "支持", "support.account.Individuation": "个性化", "support.inform.Read": "已读", "support.openapi.Api baseurl": "API 根地址", "support.openapi.Api manager": "API 密钥管理", "support.openapi.Copy success": "已复制 API 地址", "support.openapi.New api key": "新的 API 密钥", "support.openapi.New api key tip": "请保管好你的密钥，密钥不会再次展示~", "support.outlink.Delete link tip": "确认删除该免登录链接？删除后，该链接将会立即失效，对话日志仍会保留，请确认！", "support.outlink.Max usage points": "积分上限", "support.outlink.Max usage points tip": "该链接最多允许使用多少积分，超出后将无法使用。-1 代表无限制。", "support.outlink.Usage points": "积分消耗", "support.outlink.share.Chat_quote_reader": "全文阅读器", "support.outlink.share.Full_text tips": "允许阅读该引用片段来源的完整数据集", "support.outlink.share.Response Quote": "引用内容", "support.outlink.share.Response Quote tips": "查看知识库搜索的引用内容，不可查看完整引用文档或跳转引用网站", "support.outlink.share.running_node": "运行节点", "support.outlink.share.show_complete_quote": "查看来源原文", "support.outlink.share.show_complete_quote_tips": "查看及下载完整引用文档，或跳转引用网站", "support.permission.Permission": "权限", "support.standard.AI Bonus Points": "AI 积分", "support.standard.due_date": "到期时间", "support.standard.storage": "存储量", "support.standard.type": "类型", "support.team.limit.No permission rerank": "无权使用结果重排，请升级您的套餐", "support.user.Avatar": "头像", "support.user.Go laf env": "点击前往 {{env}} 获取 PAT 凭证。", "support.user.Laf account course": "查看绑定 laf 账号教程。", "support.user.Laf account intro": "绑定你的 laf 账号后，你将可以在工作流中使用 laf 模块，实现在线编写代码。", "support.user.Need to login": "请先登录", "support.user.Price": "计费标准", "support.user.User self info": "个人信息", "support.user.auth.Sending Code": "正在发送", "support.user.auth.get_code": "获取验证码", "support.user.auth.get_code_again": "s后重新获取", "support.user.captcha_placeholder": "请输入验证码", "support.user.info.bind_notification_error": "绑定通知账号异常", "support.user.info.bind_notification_hint": "请绑定通知接收账号，以确保您能正常接收套餐过期提醒等通知，保障您的服务正常运行。", "support.user.info.bind_notification_success": "绑定通知账号成功", "support.user.info.code_required": "验证码不能为空", "support.user.info.notification_receiving_hint": "通知接收", "support.user.info.verification_code": "验证码", "support.user.inform.System message": "系统消息", "support.user.login.Email": "邮箱", "support.user.login.Github": "GitHub 登录", "support.user.login.Google": "Google 登录", "support.user.login.Microsoft": "微软登录", "support.user.login.Password": "密码", "support.user.login.Password login": "密码登录", "support.user.login.Phone": "手机号登录", "support.user.login.Phone number": "手机号", "support.user.login.Provider error": "登录异常，请重试", "support.user.login.Username": "用户名", "support.user.login.Wechat": "微信登录", "support.user.login.can_not_login": "无法登录，点击联系", "support.user.login.error": "登录异常", "support.user.login.security_failed": "安全校验失败", "support.user.login.wx_qr_login": "微信扫码登录", "support.user.logout.confirm": "确认退出登录?", "support.user.team.Dataset usage": "知识库容量", "support.user.team.Team Tags Async Success": "同步完成", "support.user.team.member": "用户", "support.wallet.Ai point every thousand tokens": "{{points}} 积分/1K tokens", "support.wallet.Ai point every thousand tokens_input": "输入：{{points}} 积分/1K tokens", "support.wallet.Ai point every thousand tokens_output": "输出：{{points}} 积分/1K tokens", "support.wallet.Amount": "金额", "support.wallet.App_amount_not_sufficient": "您的应用数量已达上限，请升级套餐后继续使用。", "support.wallet.Buy": "购买", "support.wallet.Dataset_amount_not_sufficient": "您的知识库数量已达上限，请升级套餐后继续使用。", "support.wallet.Dataset_not_sufficient": "您的知识库容量不足，请先升级套餐或购买额外知识库容量后继续使用。", "support.wallet.Not sufficient": "您的 AI 积分不足，请先升级套餐或购买额外 AI 积分后继续使用。", "support.wallet.Plan expired time": "套餐到期时间", "support.wallet.Standard Plan Detail": "套餐详情", "support.wallet.Team_member_over_size": "您的团队用户数量已达上限，请升级套餐后继续使用。", "support.wallet.To read plan": "查看套餐", "support.wallet.amount_0": "购买数量不能为0", "support.wallet.apply_invoice": "申请开票", "support.wallet.bill.Number": "订单号", "support.wallet.bill.Status": "状态", "support.wallet.bill.Type": "订单类型", "support.wallet.bill.payWay.Way": "支付方式", "support.wallet.bill.payWay.alipay": "支付宝支付", "support.wallet.bill.payWay.balance": "余额支付", "support.wallet.bill.payWay.bank": "对公支付", "support.wallet.bill.payWay.wx": "微信支付", "support.wallet.bill.status.closed": "已关闭", "support.wallet.bill.status.notpay": "未支付", "support.wallet.bill.status.refund": "已退款", "support.wallet.bill.status.success": "支付成功", "support.wallet.bill_detail": "账单详情", "support.wallet.bill_tag.bill": "账单记录", "support.wallet.bill_tag.default_header": "默认抬头", "support.wallet.bill_tag.invoice": "开票记录", "support.wallet.billable_invoice": "可开票账单", "support.wallet.buy_resource": "购买资源包", "support.wallet.has_invoice": "是否已开票", "support.wallet.invoice_amount": "开票金额", "support.wallet.invoice_data.bank": "开户银行", "support.wallet.invoice_data.bank_account": "开户账号", "support.wallet.invoice_data.company_address": "公司地址", "support.wallet.invoice_data.company_phone": "公司电话", "support.wallet.invoice_data.email": "邮箱地址", "support.wallet.invoice_data.need_special_invoice": "是否需要专票", "support.wallet.invoice_data.organization_name": "组织名称", "support.wallet.invoice_data.unit_code": "统一信用代码", "support.wallet.invoice_detail": "发票详情", "support.wallet.invoice_info": "发票将在 3-7 个工作日内发送至邮箱，请耐心等待", "support.wallet.invoicing": "开票", "support.wallet.moduleName.qa": "QA 拆分", "support.wallet.noBill": "无账单记录~", "support.wallet.no_invoice": "暂无开票记录", "support.wallet.subscription.AI points": "AI 积分", "support.wallet.subscription.AI points click to read tip": "每次调用 AI 模型时，都会消耗一定的 AI 积分（类似于 token）。点击可查看详细计算规则。", "support.wallet.subscription.AI points usage": "AI 积分使用量", "support.wallet.subscription.AI points usage tip": "每次调用 AI 模型时，都会消耗一定的 AI 积分。具体的计算标准可参考上方的“计费标准”", "support.wallet.subscription.Ai points": "AI 积分计算标准", "support.wallet.subscription.Current plan": "当前套餐", "support.wallet.subscription.Extra ai points": "额外 AI 积分", "support.wallet.subscription.Extra dataset size": "额外知识库容量", "support.wallet.subscription.Extra plan": "额外资源包", "support.wallet.subscription.Extra plan tip": "标准套餐不够时，您可以购买额外资源包继续使用", "support.wallet.subscription.FAQ": "常见问题", "support.wallet.subscription.Month amount": "月数", "support.wallet.subscription.Next plan": "未来套餐", "support.wallet.subscription.Stand plan level": "订阅套餐", "support.wallet.subscription.Sub plan": "订阅套餐", "support.wallet.subscription.Sub plan tip": "免费使用【{{title}}】或升级更高的套餐", "support.wallet.subscription.Team plan and usage": "套餐与用量", "support.wallet.subscription.Training weight": "训练优先级：{{weight}}", "support.wallet.subscription.Update extra ai points": "额外 AI 积分", "support.wallet.subscription.Update extra dataset size": "额外存储量", "support.wallet.subscription.Upgrade plan": "升级套餐", "support.wallet.subscription.ai_model": "AI语言模型", "support.wallet.subscription.function.History store": "{{amount}} 天对话记录保留", "support.wallet.subscription.function.Max app": "{{amount}} 个应用上限", "support.wallet.subscription.function.Max dataset": "{{amount}} 个知识库上限", "support.wallet.subscription.function.Max dataset size": "{{amount}} 组知识库索引", "support.wallet.subscription.function.Max members": "{{amount}} 个团队用户上限", "support.wallet.subscription.function.Points": "{{amount}} AI 积分", "support.wallet.subscription.mode.Month": "按月", "support.wallet.subscription.mode.Period": "订阅周期", "support.wallet.subscription.mode.Year": "按年", "support.wallet.subscription.mode.Year sale": "赠送两个月", "support.wallet.subscription.point": "积分", "support.wallet.subscription.standardSubLevel.custom": "自定义版", "support.wallet.subscription.standardSubLevel.enterprise": "企业版", "support.wallet.subscription.standardSubLevel.enterprise_desc": "适合中小企业在生产环境构建知识库应用", "support.wallet.subscription.standardSubLevel.experience": "体验版", "support.wallet.subscription.standardSubLevel.experience_desc": "可解锁 FastGPT 完整功能", "support.wallet.subscription.standardSubLevel.free": "免费版", "support.wallet.subscription.standardSubLevel.free desc": "核心功能免费试用。30 天未登录，将会清空知识库。", "support.wallet.subscription.standardSubLevel.team": "团队版", "support.wallet.subscription.standardSubLevel.team_desc": "适合小团队构建知识库应用并提供对外服务", "support.wallet.subscription.status.active": "生效中", "support.wallet.subscription.status.expired": "已过期", "support.wallet.subscription.status.inactive": "待使用", "support.wallet.subscription.team_operation_log": "记录团队操作日志", "support.wallet.subscription.token_compute": "点击查看在线 Tokens 计算器", "support.wallet.subscription.type.balance": "余额充值", "support.wallet.subscription.type.extraDatasetSize": "知识库扩容", "support.wallet.subscription.type.extraPoints": "AI 积分套餐", "support.wallet.subscription.type.standard": "套餐订阅", "support.wallet.subscription.web_site_sync": "Web 站点同步", "support.wallet.usage.Ai model": "AI 模型", "support.wallet.usage.App name": "应用名", "support.wallet.usage.Audio Speech": "语音播放", "support.wallet.usage.Bill Module": "扣费模块", "support.wallet.usage.Duration": "时长（秒）", "support.wallet.usage.Module name": "模块名", "support.wallet.usage.Source": "来源", "support.wallet.usage.Text Length": "文本长度", "support.wallet.usage.Time": "生成时间", "support.wallet.usage.Token Length": "token 长度", "support.wallet.usage.Total": "总金额", "support.wallet.usage.Total points": "AI 积分消耗", "support.wallet.usage.Usage Detail": "使用详情", "support.wallet.usage.Whisper": "语音输入", "sync_link": "同步链接", "sync_success": "同步成功", "system.Concat us": "联系我们", "system.Help Document": "帮助文档", "system_help_chatbot": "机器人助手", "tag_list": "标签列表", "team_tag": "团队标签", "templateTags.Image_generation": "图片生成", "templateTags.Office_services": "办公服务", "templateTags.Roleplay": "角色扮演", "templateTags.Web_search": "联网搜索", "templateTags.Writing": "文本创作", "template_market": "模板市场", "textarea_variable_picker_tip": "输入\"/\"可选择变量", "to_dataset": "前往知识库", "ui.textarea.Magnifying": "放大", "un_used": "未使用", "unauth_token": "凭证已过期，请重新登录", "undo_tip": "撤销  ctrl  z", "undo_tip_mac": "撤销  ⌘  z ", "unit.character": "字符", "unit.minute": "分钟", "unit.seconds": "秒", "unknow_source": "未知来源", "unusable_variable": "无可用变量", "update_failed": "更新异常", "update_success": "更新成功", "upload_file": "上传文件", "upload_file_error": "上传文件失败", "use_helper": "使用帮助", "user.Account": "账号", "user.Amount of earnings": "收益（￥）", "user.Amount of inviter": "累计邀请人数", "user.Application Name": "项目名", "user.Avatar": "头像", "user.Change": "变更", "user.Copy invite url": "复制邀请链接", "user.Edit name": "点击修改昵称", "user.Invite Url": "邀请链接", "user.Invite url tip": "通过该链接注册的好友将永久与你绑定，其充值时你会获得一定余额奖励。\n此外，好友使用手机号注册时，你将立即获得 5 元奖励。\n奖励会发送到您的默认团队中。", "user.Laf Account Setting": "laf 账号配置", "user.Language": "语言", "user.Member Name": "昵称", "user.No_right_to_reset_password": "没有重置密码的权限", "user.Notification Receive": "通知接收", "user.Notification Receive Bind": "请先绑定通知接收途径", "user.Old password is error": "旧密码错误", "user.OpenAI Account Setting": "OpenAI 账号配置", "user.Password": "密码", "user.Password has no change": "新密码和旧密码重复", "user.Pay": "充值", "user.Promotion": "促销", "user.Promotion Rate": "返现比例", "user.Promotion rate tip": "好友充值时你将获得一定比例的余额奖励", "user.Replace": "更换", "user.Set OpenAI Account Failed": "设置 OpenAI 账号异常", "user.Team": "团队", "user.Time": "时间", "user.Timezone": "时区", "user.Update Password": "修改密码", "user.Update password failed": "修改密码异常", "user.Update password successful": "修改密码成功", "user.apikey.key": "API 密钥", "user.confirm_password": "确认密码", "user.init_password": "请初始化密码", "user.new_password": "新密码", "user.no_invite_records": "暂无邀请记录", "user.no_notice": "暂无通知", "user.no_usage_records": "暂无使用记录", "user.old_password": "旧密码", "user.password_message": "密码最少 4 位最多 60 位", "user.password_tip": "密码至少 8 位，且至少包含两种组合：数字、字母或特殊字符", "user.reset_password": "重置密码", "user.reset_password_tip": "未设置初始密码/长时间未修改密码，请重置密码", "user.team.Balance": "团队余额", "user.team.Check Team": "切换", "user.team.Leave Team": "离开团队", "user.team.Leave Team Failed": "离开团队异常", "user.team.Member": "用户", "user.team.Member Name": "用户名", "user.team.Over Max Member Tip": "团队最多 {{max}} 人", "user.team.Personal Team": "个人团队", "user.team.Processing invitations": "处理邀请", "user.team.Processing invitations Tips": "你有 {{amount}} 个需要处理的团队邀请", "user.team.Remove Member Confirm Tip": "确认将 {{username}} 移出团队？", "user.team.Select Team": "团队选择", "user.team.Switch Team Failed": "切换团队异常", "user.team.Tags Async": "保存", "user.team.Team Tags Async": "标签同步", "user.team.Team Tags Async Success": "链接报错成功，标签信息更新", "user.team.invite.Accepted": "已加入团队", "user.team.invite.Deal Width Footer Tip": "处理完会自动关闭噢~", "user.team.invite.Reject": "已拒绝邀请", "user.team.member.Confirm Leave": "确认离开该团队？\n退出后，您在该团队所有的资源（ 应用、知识库、文件夹、管理的角色等）均转让给团队所有者。", "user.team.member.active": "已加入", "user.team.member.reject": "拒绝", "user.team.member.waiting": "待接受", "user.team.role.Admin": "管理员", "user.team.role.Owner": "创建者", "user.team.role.Visitor": "访客", "user.team.role.writer": "可写用户", "user.type": "类型", "user_leaved": "已离开", "value": "值", "verification": "验证", "workflow.template.communication": "通信", "xx_search_result": "{{key}} 的搜索结果", "yes": "是", "yesterday": "昨天", "yesterday_detail_time": "昨天 {{time}}", "zoomin_tip": "缩小  ctrl  -", "zoomin_tip_mac": "缩小  ⌘  -", "zoomout_tip": "放大  ctrl  +", "zoomout_tip_mac": "放大  ⌘  +"}