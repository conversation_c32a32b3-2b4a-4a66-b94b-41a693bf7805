{"ai_model": "AI model", "all": "all", "app_name": "Application name", "auto_index": "Auto index", "billing_module": "Deduction module", "confirm_export": "A total of {{total}} pieces of data were filtered out. Are you sure to export?", "current_filter_conditions": "Current filter conditions", "dashboard": "Dashboard", "details": "Details", "dingtalk": "DingTalk", "duration_seconds": "Duration (seconds)", "embedding_index": "Embedding", "every_day": "Day", "every_month": "Moon", "every_week": "weekly", "export_confirm": "Export confirmation", "export_confirm_tip": "There are currently {{total}} usage records in total. Are you sure to export?", "export_success": "Export successfully", "export_title": "Time,Employees,Type,Project name,AI points", "feishu": "<PERSON><PERSON><PERSON>", "generation_time": "Generation time", "image_index": "Image index", "image_parse": "Image tagging", "input_token_length": "input tokens", "llm_paragraph": "LLM segmentation", "mcp": "MCP call", "member": "employee", "member_name": "Member name", "module_name": "module name", "month": "moon", "no_usage_records": "No usage record yet", "official_account": "Official Account", "order_number": "Order number", "output_token_length": "output tokens", "pages": "Pages", "pdf_enhanced_parse": "PDF Enhanced Analysis", "pdf_parse": "PDF Analysis", "points": "Points", "project_name": "Project name", "qa": "QA", "select_member_and_source_first": "Please select employees and types first", "share": "Share Link", "source": "source", "text_length": "text length", "token_length": "token length", "total_points": "AI points consumption", "total_points_consumed": "AI points consumption", "total_usage": "Total Usage", "usage_detail": "Details", "user_type": "type", "wecom": "WeCom"}