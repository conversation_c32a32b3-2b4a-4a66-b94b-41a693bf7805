{"Action": "Action", "Add": "Add", "Add_new_input": "Add new input", "All": "All", "App": "Application", "Cancel": "Cancel", "Choose": "<PERSON><PERSON>", "Click_to_expand": "Click to expand", "Close": "Close", "Code": "Code", "Config": "Configuration", "Confirm": "Confirm", "Continue_Adding": "Continue adding", "Copy": "Copy", "Creating": "Creating", "Delete": "Delete", "Detail": "Detail", "Documents": "Documents", "Done": "Done", "Download": "Download", "Edit": "Edit", "Error": "Error", "Exit": "Exit", "Export": "Export", "FAQ.ai_point_a": "Each time an AI model is called, a certain amount of AI points will be consumed. \nFor specific calculation standards, please refer to the \"AI integral calculation standards\" above. \nThe system will give priority to the actual usage returned by the model manufacturer. If it is empty, the calculation method of GPT3.5 is used for estimation. 1Token≈0.7 Chinese characters ≈0.9 English words, and the characters that appear continuously may be considered as 1 Tokens.", "FAQ.ai_point_expire_a": "Yes, they will expire. After the current package expires, the AI points will be reset to the new package's AI points. Annual package AI points are valid for one year, not monthly.", "FAQ.ai_point_expire_q": "Do AI points expire?", "FAQ.ai_point_q": "What are AI points?", "FAQ.check_subscription_a": "Go to Account - Personal Information - Package Details - Usage. You can view the effective and expiration dates of your subscribed packages. After the paid package expires, it will automatically switch to the free version.", "FAQ.check_subscription_q": "Where can I view my subscribed packages?", "FAQ.dataset_compute_a": "1 knowledge base storage is equal to 1 knowledge base index. \nA single chunked data usually corresponds to multiple indexes. You can see \"n role indexes\" in a single knowledge base collection.", "FAQ.dataset_compute_q": "How is Dataset storage calculated?", "FAQ.dataset_index_a": "No, but if the Dataset index exceeds the limit, you cannot insert or update Dataset content.", "FAQ.dataset_index_q": "Will the Dataset index be deleted if it exceeds the limit?", "FAQ.free_user_clean_a": "If a free team (free version and has not purchased additional packages) does not log in to the system for 30 consecutive days, the system will automatically clear all Dataset content under that team.", "FAQ.free_user_clean_q": "Will the data of the free version be cleared?", "FAQ.package_overlay_a": "Yes, each purchased resource pack is independent and will be used in an overlapping manner within its validity period. AI points will be deducted from the resource pack that expires first.", "FAQ.package_overlay_q": "Can additional resource packs be stacked?", "FAQ.switch_package_a": "The package usage rule is to prioritize the use of higher-level packages. Therefore, if the newly purchased package is higher than the current package, the new package will take effect immediately; otherwise, the current package will continue to be used.", "FAQ.switch_package_q": "Will the subscription package be switched?", "File": "File", "Finish": "Finish", "Folder": "Folder", "FullScreen": "FullScreen", "FullScreenLight": "FullScreenLight", "Import": "Import", "Input": "Input", "Instructions": "Instruction", "Intro": "Introduction", "Loading": "Loading...", "Login": "<PERSON><PERSON>", "More": "More", "Move": "Move", "Name": "Name", "None": "None", "OK": "OK", "Open": "Open", "Operation": "Operation", "Other": "Other", "Output": "Output", "Params": "Parameters", "Parse": "Analysis", "Permission": "Permission", "Permission_tip": "Individual permissions are greater than role permissions", "Preview": "Preview", "Remove": "Remove", "Rename": "<PERSON><PERSON>", "Required_input": "Required", "Reset": "Reset", "Restart": "<PERSON><PERSON>", "Resume": "Resume", "Role": "Permission", "Run": "Run", "Running": "Running", "Save": "Save", "Save_and_exit": "Save and Exit", "Search": "Search", "Select_all": "Select all", "Setting": "Setting", "Status": "Status", "Submit": "Submit", "Success": "Success", "Team": "Team", "UnKnow": "Unknown", "Unlimited": "Unlimited", "Update": "Update", "Username": "Username", "Waiting": "Waiting", "Warning": "Warning", "Website": "Website", "action_confirm": "Confirm", "add_new": "add_new", "add_new_param": "Add new param", "add_success": "Added Successfully", "all_quotes": "All quotes", "all_result": "Full Results", "app_not_version": "This application has not been published, please publish it first", "auth_config": "Authentication", "auth_type": "Authentication type", "auth_type.Custom": "Customize", "auth_type.None": "None", "back": "Back", "base_config": "Basic Configuration", "bill_already_processed": "Order has been processed", "bill_expired": "Order expired", "bill_not_pay_processed": "Non-online orders", "button.extra_dataset_size_tip": "You are purchasing [Extra Knowledge Base Capacity]", "button.extra_points_tip": "You are purchasing [Extra AI Points]", "can_copy_content_tip": "It is not possible to copy automatically using the browser, please manually copy the following content", "chart_mode_cumulative": "Cumulative", "chart_mode_incremental": "Incremental", "choosable": "Choosable", "chose_condition": "Choose Condition", "chosen": "<PERSON><PERSON>", "classification": "Classification", "click_drag_tip": "Click to Drag", "click_select_avatar": "Click to Select Avatar", "click_to_copy": "Click to copy", "click_to_resume": "Click to Resume", "code_editor": "Code Editor", "code_error.account_error": "Incorrect account name or password", "code_error.account_exist": "Account has been registered", "code_error.account_not_found": "User is not registered", "code_error.app_error.invalid_app_type": "Invalid Application Type", "code_error.app_error.invalid_owner": "Unauthorized Application Owner", "code_error.app_error.not_exist": "Application Does Not Exist", "code_error.app_error.un_auth_app": "Unauthorized to Operate This Application", "code_error.chat_error.un_auth": "Unauthorized to Operate This Chat Record", "code_error.error_code.400": "Request Failed", "code_error.error_code.401": "No Access Permission", "code_error.error_code.403": "Access Forbidden", "code_error.error_code.404": "Request Not Found", "code_error.error_code.405": "Request Method Error", "code_error.error_code.406": "Request Format Error", "code_error.error_code.410": "Resource Deleted", "code_error.error_code.422": "Validation Error", "code_error.error_code.500": "Server Error", "code_error.error_code.502": "Gateway Error", "code_error.error_code.503": "Server Overloaded or Under Maintenance", "code_error.error_code.504": "Gateway Timeout", "code_error.error_code[429]": "Requests are too frequent", "code_error.error_message.403": "Credential Error", "code_error.error_message.510": "Insufficient Account Balance", "code_error.error_message.511": "Unauthorized to Operate This Model", "code_error.error_message.513": "Unauthorized to Read This File", "code_error.error_message.514": "Invalid API Key", "code_error.openapi_error.api_key_not_exist": "API Key Does Not Exist", "code_error.openapi_error.exceed_limit": "Up to 10 API Keys", "code_error.openapi_error.un_auth": "Unauthorized to Operate This API Key", "code_error.outlink_error.invalid_link": "Invalid Share Link", "code_error.outlink_error.link_not_exist": "Share Link Does Not Exist", "code_error.outlink_error.un_auth_user": "Identity Verification Failed", "code_error.plugin_error.not_exist": "The tool does not exist", "code_error.plugin_error.un_auth": "No permission to operate the tool", "code_error.system_error.community_version_num_limit": "Exceeded Open Source Version Limit, Please Upgrade to Commercial Version: https://tryfastgpt.ai", "code_error.system_error.license_app_amount_limit": "Exceed the maximum number of applications in the system", "code_error.system_error.license_dataset_amount_limit": "Exceed the maximum number of knowledge bases in the system", "code_error.system_error.license_user_amount_limit": "Exceed the maximum number of users in the system", "code_error.team_error.ai_points_not_enough": "Insufficient AI Points", "code_error.team_error.app_amount_not_enough": "Application Limit Reached", "code_error.team_error.cannot_delete_default_group": "Cannot delete default role", "code_error.team_error.cannot_delete_non_empty_org": "Cannot delete non-empty organization", "code_error.team_error.cannot_modify_root_org": "Cannot modify root organization", "code_error.team_error.cannot_move_to_sub_path": "Cannot move to same or subdirectory", "code_error.team_error.dataset_amount_not_enough": "Dataset Limit Reached", "code_error.team_error.dataset_size_not_enough": "Insufficient Dataset Capacity, Please Expand", "code_error.team_error.group_name_duplicate": "Duplicate role name", "code_error.team_error.group_name_empty": "Role name cannot be empty", "code_error.team_error.group_not_exist": "Role does not exist", "code_error.team_error.invitation_link_invalid": "The invitation link has expired", "code_error.team_error.not_user": "The employee cannot be found", "code_error.team_error.org_member_duplicated": "Duplicate organization employee", "code_error.team_error.org_member_not_exist": "Organization employee does not exist", "code_error.team_error.org_not_exist": "Organization does not exist", "code_error.team_error.org_parent_not_exist": "Parent organization does not exist", "code_error.team_error.over_size": "Team members exceed limit", "code_error.team_error.plugin_amount_not_enough": "Plugin <PERSON> Reached", "code_error.team_error.re_rank_not_enough": "Search rearrangement cannot be used in the free version~", "code_error.team_error.too_many_invitations": "You have reached the maximum number of active invitation links, please clean up some links first", "code_error.team_error.un_auth": "Unauthorized to Operate This Team", "code_error.team_error.user_not_active": "The user did not accept or has left the team", "code_error.team_error.website_sync_not_enough": "The free version cannot be synchronized with the web site ~", "code_error.team_error.you_have_been_in_the_team": "You are already in this team", "code_error.token_error_code.403": "Invalid Login Status, Please Re-login", "code_error.user_error.balance_not_enough": "Insufficient Account Balance", "code_error.user_error.bin_visitor_guest": "You Are Currently a Guest, Unauthorized to Operate", "code_error.user_error.un_auth_user": "User Not Found", "comfirm_import": "Confirm import", "comfirm_leave_page": "Confirm to Leave This Page?", "comfirn_create": "Confirm Creation", "commercial_function_tip": "Please Upgrade to the Commercial Version to Use This Feature: https://doc.fastgpt.cn/docs/commercial/intro/", "comon.Continue_Adding": "Continue Adding", "compliance.chat": "The content is generated by third-party AI and cannot be guaranteed to be true and accurate. It is for reference only.", "compliance.dataset": "Please ensure that your content strictly complies with relevant laws and regulations and avoid containing any illegal or infringing content. \nPlease be careful when uploading materials that may contain sensitive information.", "confirm_choice": "Confirm Choice", "confirm_move": "Move Here", "confirm_update": "confirm_update", "contact_way": "Notification Received", "contribute_app_template": "Contribute Template", "copy_successful": "Copied Successfully", "copy_to_clipboard": "Copy to Clipboard", "core.Chat": "Cha<PERSON>", "core.ai.Max context": "Max Context", "core.ai.Model": "Model", "core.ai.Not deploy rerank model": "Re-rank Model Not Deployed", "core.ai.Prompt": "Prompt", "core.ai.Support tool": "Function Call", "core.ai.model.Dataset Agent Model": "File read model", "core.ai.model.Vector Model": "Index model", "core.ai.model.doc_index_and_dialog": "Document Index & Dialog Index", "core.app.Api request": "API Request", "core.app.Api request desc": "Integrate into existing systems through API, or WeChat Work, Feishu, etc.", "core.app.App intro": "App Introduction", "core.app.Auto execute": "Auto execute", "core.app.Chat Variable": "Chat Variable", "core.app.Config schedule plan": "Configure Scheduled Execution", "core.app.Config whisper": "Configure Voice Input", "core.app.Config_auto_execute": "Click to configure automatic execution rules", "core.app.Interval timer config": "Scheduled Execution Configuration", "core.app.Interval timer run": "Scheduled Execution", "core.app.Interval timer tip": "Can Execute App on Schedule", "core.app.Make a brief introduction of your app": "Give Your AI App an Introduction", "core.app.Name and avatar": "Avatar & Name", "core.app.Publish": "Publish", "core.app.Publish Confirm": "Confirm to Publish App? This Will Immediately Update the App Status on All Publishing Channels.", "core.app.Publish app tip": "After Publishing the App, All Publishing Channels Will Immediately Use This Version", "core.app.QG.Custom prompt tip": "To ensure the generated content follows the correct format, [Yellow Prompt] cannot be modified", "core.app.QG.Custom prompt tip1": "To ensure the generated content follows the correct format, ", "core.app.QG.Custom prompt tip2": "[Yellow Prompt]", "core.app.QG.Custom prompt tip3": " cannot be modified", "core.app.QG.Fixed Prompt": "Please strictly follow the format rules: \nReturn questions in JSON format: ['Question 1', 'Question 2', 'Question 3'].", "core.app.Question Guide": "Guess What You Want to Ask", "core.app.Quote prompt": "Quote Template Prompt", "core.app.Quote templates": "Quote Content Templates", "core.app.Random": "Divergent", "core.app.Search team tags": "Search Tags", "core.app.Select TTS": "Select Voice Playback Mode", "core.app.Select quote template": "Select Quote Prompt Template", "core.app.Set a name for your app": "Set a Name for Your App", "core.app.Setting ai property": "Click to Configure AI Model Properties", "core.app.Share link": "Login-Free Window", "core.app.Share link desc": "Share the link with other users, they can use it directly without logging in", "core.app.Share link desc detail": "You can directly share this model with other users for conversation, they can use it directly without logging in. Note, this feature will consume your account balance, please keep the link safe!", "core.app.TTS": "Voice Playback", "core.app.TTS Tip": "After enabling, you can use the voice playback function after each conversation. Using this feature may incur additional costs.", "core.app.TTS start": "Read Content", "core.app.Team tags": "Team Tags", "core.app.Tool call": "Tool Call", "core.app.ToolCall.No plugin": "No Available Plugins", "core.app.ToolCall.Parameter setting": "Input Parameters", "core.app.ToolCall.System": "System", "core.app.ToolCall.Team": "Team", "core.app.Welcome Text": "Conversation Opening", "core.app.Whisper": "Voice Input", "core.app.Whisper config": "Voice Input Configuration", "core.app.deterministic": "Deterministic", "core.app.edit.Prompt Editor": "Prompt Editor", "core.app.edit.Query extension background prompt": "Conversation Background Description", "core.app.edit.Query extension background tip": "Describe the scope of the current conversation to help the AI complete and extend the current question. The content you fill in is usually for this assistant.", "core.app.edit_content": "Edit App Information", "core.app.error.App name can not be empty": "App Name Cannot Be Empty", "core.app.error.Get app failed": "Failed to Retrieve App", "core.app.feedback.Custom feedback": "Custom Feedback", "core.app.feedback.close custom feedback": "<PERSON> Feedback", "core.app.have_saved": "Saved", "core.app.logs.Source And Time": "Source & Time", "core.app.more": "View More", "core.app.no_app": "No Apps Yet, Create One Now!", "core.app.not_saved": "Not Saved", "core.app.outLink.Can Drag": "Icon Can Be Dragged", "core.app.outLink.Default open": "Default Open", "core.app.outLink.Iframe block title": "Copy the iframe below to add to your website", "core.app.outLink.Link block title": "Copy the link below to open in the browser", "core.app.outLink.Script Close Icon": "Close Icon", "core.app.outLink.Script Open Icon": "Open Icon", "core.app.outLink.Script block title": "Add the code below to your website", "core.app.outLink.Select Mode": "Start Using", "core.app.outLink.Select Using Way": "Select Usage Method", "core.app.outLink.Show History": "Show Chat History", "core.app.publish.Fei shu bot": "<PERSON><PERSON><PERSON>", "core.app.publish.Fei shu bot publish": "Publish to <PERSON><PERSON><PERSON>", "core.app.schedule.Default prompt": "Default Question", "core.app.schedule.Default prompt placeholder": "Default question when executing the app", "core.app.schedule.Every day": "Every day at {{hour}}:00", "core.app.schedule.Every month": "Every month on the {{day}} at {{hour}}:00", "core.app.schedule.Every week": "Every week on {{day}} at {{hour}}:00", "core.app.schedule.Interval": "Every {{interval}} hours", "core.app.schedule.Open schedule": "Scheduled Execution", "core.app.setting": "App Information Settings", "core.app.share.Amount limit tip": "Up to 10 roles", "core.app.share.Create link": "Create New Link", "core.app.share.Create link tip": "Creation successful. The share address has been copied and can be shared directly.", "core.app.share.Ip limit title": "IP Rate Limit (people/minute)", "core.app.share.Is response quote": "Return Quote", "core.app.share.Not share link": "No Share Link Created", "core.app.share.Role check": "Identity Verification", "core.app.switch_to_template_market": "Jump template market", "core.app.tip.Add a intro to app": "Give the app an introduction", "core.app.tip.chatNodeSystemPromptTip": "Enter a prompt here", "core.app.tip.systemPromptTip": "Fixed guide words for the model. By adjusting this content, you can guide the model's chat direction. This content will be fixed at the beginning of the context. You can use / to insert variables.\nIf a Dataset is associated, you can also guide the model when to call the Dataset search by appropriate description. For example:\nYou are an assistant for the movie 'Interstellar'. When users ask about content related to 'Interstellar', please search the Dataset and answer based on the search results.", "core.app.tip.variableTip": "Before the conversation begins, users can be asked to fill in some content as specific variables for this round of conversation. \nThis module is located after the opening boot.\n\nIn the input box, you can select variables through / activation, such as: prompt words, qualifiers, etc.", "core.app.tip.welcomeTextTip": "Before each conversation starts, send an initial content. Supports standard Markdown syntax. Additional tags that can be used:\n[Quick Key]: Users can directly send the question by clicking", "core.app.tool_label.doc": "Documentation", "core.app.tool_label.github": "GitHub Address", "core.app.tool_label.price": "Pricing", "core.app.tool_label.view_doc": "View Documentation", "core.app.tts.Speech model": "Speech Model", "core.app.tts.Speech speed": "Speech Speed", "core.app.tts.Test Listen": "Test Listen", "core.app.tts.Test Listen Text": "Hello, this is a voice test. If you can hear this sentence, the voice playback function is normal.", "core.app.whisper.Auto send": "Auto Send", "core.app.whisper.Auto send tip": "Automatically send after voice input is completed, no need to click the send button manually", "core.app.whisper.Auto tts response": "Auto Voice Response", "core.app.whisper.Auto tts response tip": "The question sent by voice input will be directly responded to in voice form. Please ensure that the voice playback function is enabled.", "core.app.whisper.Close": "Close", "core.app.whisper.Not tts tip": "You have not enabled voice playback, this feature cannot be used", "core.app.whisper.Open": "Open", "core.app.whisper.Switch": "Enable Voice Input", "core.chat.Admin Mark Content": "Corrected Reply", "core.chat.Audio Not Support": "<PERSON><PERSON> Does Not Support Voice Playback", "core.chat.Audio Speech Error": "Voice Playback <PERSON><PERSON><PERSON>", "core.chat.Cancel Speak": "Cancel Voice Input", "core.chat.Confirm to clear history": "Confirm to Clear Online Chat History for This App? Share and API Call Records Will Not Be Cleared.", "core.chat.Confirm to clear share chat history": "Confirm to Delete All Chat Records?", "core.chat.Converting to text": "Converting to Text...", "core.chat.Custom History Title": "Custom History Title", "core.chat.Custom History Title Description": "If set to empty, it will automatically follow the chat record.", "core.chat.Exit Chat": "Exit Chat", "core.chat.Failed to initialize chat": "Failed to Initialize Chat", "core.chat.Feedback Failed": "Feedback Submission Failed", "core.chat.Feedback Modal": "<PERSON><PERSON>t <PERSON>", "core.chat.Feedback Modal Tip": "Enter the part you are not satisfied with the answer", "core.chat.Feedback Submit": "Submit <PERSON>", "core.chat.Feedback Success": "Feedback Successful!", "core.chat.Finish Speak": "Voice Input Completed", "core.chat.History": "History", "core.chat.History Amount": "{{amount}} Records", "core.chat.Mark": "<PERSON> Expected Answer", "core.chat.Mark Description": "The current marking function is in beta.\n\nAfter clicking to add a mark, you need to select a Dataset to store the marked data. You can quickly mark questions and expected answers through this function to guide the model's next answer.\n\nCurrently, the marking function is the same as other data in the Dataset and is affected by the model, which does not mean that it will 100% meet expectations after marking.\n\nMarking data is only synchronized with the Dataset in one direction. If the Dataset modifies the marked data, the marked data displayed in the log cannot be synchronized.", "core.chat.Mark Description Title": "Marking Function Introduction", "core.chat.New Chat": "New Chat", "core.chat.Pin": "<PERSON>n", "core.chat.Question Guide": "Guess What You Want to Ask", "core.chat.Quote": "Quote", "core.chat.Quote Amount": "Dataset Quotes ({{amount}} Records)", "core.chat.Read Mark Description": "View Marking Function Introduction", "core.chat.Recent use": "Recently Used", "core.chat.Record": "Voice Input", "core.chat.Restart": "<PERSON><PERSON>", "core.chat.Run test": "Run Preview", "core.chat.Select dataset": "Select Dataset", "core.chat.Select dataset Desc": "Select a Dataset to store the expected answer", "core.chat.Send Message": "Send", "core.chat.Speaking": "I'm Listening, Please Speak...", "core.chat.Start Chat": "Start Chat", "core.chat.Type a message": "Enter a Question, Press [Enter] to Send / Press [Ctrl(Alt/Shift) + Enter] for New Line", "core.chat.Unpin": "Unpin", "core.chat.You need to a chat app": "You Do Not Have an Available App", "core.chat.error.Chat error": "<PERSON><PERSON>", "core.chat.error.Messages empty": "API Content is Empty, Possibly Due to Text Being Too Long", "core.chat.error.Select dataset empty": "You Have Not Selected a Dataset", "core.chat.error.User input empty": "User Question Input is Empty", "core.chat.error.data_error": "Data Retrieval Error", "core.chat.feedback.Close User Like": "User A<PERSON><PERSON><PERSON>lick to Close This Mark", "core.chat.feedback.Feedback Close": "<PERSON> Feedback", "core.chat.feedback.No Content": "User Did Not Provide Specific Feedback Content", "core.chat.feedback.Read User dislike": "User Disagrees\nClick to View Content", "core.chat.logs.api": "API Call", "core.chat.logs.feishu": "<PERSON><PERSON><PERSON>", "core.chat.logs.free_login": "No login link", "core.chat.logs.mcp": "MCP call", "core.chat.logs.official_account": "Official Account", "core.chat.logs.online": "Online Use", "core.chat.logs.share": "External Link Call", "core.chat.logs.team": "Team Space Chat", "core.chat.logs.test": "Online debugging", "core.chat.logs.wecom": "WeChat Work", "core.chat.markdown.Edit Question": "Edit Question", "core.chat.markdown.Quick Question": "Click to Ask Immediately", "core.chat.markdown.Send Question": "Send Question", "core.chat.module_unexist": "Running failed: Application missing components", "core.chat.quote.Quote Tip": "Only the actual quoted content is displayed here. If the data is updated, it will not be updated in real-time here.", "core.chat.quote.Read Quote": "View Quote", "core.chat.quote.afterUpdate": "After update", "core.chat.quote.beforeUpdate": "Before update", "core.chat.response.Complete Response": "Complete Response", "core.chat.response.Extension model": "Question Optimization Model", "core.chat.response.Read complete response": "View Details", "core.chat.response.Read complete response tips": "Click to View Detailed Process", "core.chat.response.Tool call input tokens": "Tool Call Input Tokens Consumption", "core.chat.response.Tool call output tokens": "Tool Call Output Tokens Consumption", "core.chat.response.Tool call tokens": "Tool Call Tokens Consumption", "core.chat.response.context total length": "Total Context Length", "core.chat.response.loop_input": "Loop Input Array", "core.chat.response.loop_input_element": "Loop Input Element", "core.chat.response.loop_output": "Loop Output Array", "core.chat.response.loop_output_element": "Loop Output Element", "core.chat.response.module cq": "Question Classification List", "core.chat.response.module cq result": "Classification Result", "core.chat.response.module extract description": "Extract Background Description", "core.chat.response.module extract result": "Extraction Result", "core.chat.response.module historyPreview": "History Preview (Only Partial Content Displayed)", "core.chat.response.module http result": "Response Body", "core.chat.response.module if else Result": "Condition Result", "core.chat.response.module limit": "Single Search Limit", "core.chat.response.module maxToken": "Max Response Tokens", "core.chat.response.module model": "Model", "core.chat.response.module name": "Model Name", "core.chat.response.module query": "Question/Search Term", "core.chat.response.module similarity": "Similarity", "core.chat.response.module temperature": "Temperature", "core.chat.response.module time": "Run Time", "core.chat.response.plugin output": "Plugin Output Value", "core.chat.response.search using reRank": "Result Re-Rank", "core.chat.response.text output": "Text Output", "core.chat.response.update_var_result": "Variable Update Result (Displays Multiple Variable Update Results in Order)", "core.chat.response.user_select_result": "User Selection Result", "core.chat.retry": "Regenerate", "core.chat.tts.Stop Speech": "Stop", "core.dataset.Choose Dataset": "Associate Dataset", "core.dataset.Collection": "Dataset", "core.dataset.Create dataset": "Create a {{name}}", "core.dataset.Dataset": "Dataset", "core.dataset.Dataset ID": "Dataset ID", "core.dataset.Delete Confirm": "Confirm to Delete This Dataset? Data Cannot Be Recovered After Deletion, Please Confirm!", "core.dataset.Empty Dataset": "Empty Dataset", "core.dataset.Empty Dataset Tips": "No Dataset Yet, Create One Now!", "core.dataset.Folder placeholder": "This is a Directory", "core.dataset.Intro Placeholder": "This Dataset Has No Introduction Yet", "core.dataset.My Dataset": "My Dataset", "core.dataset.Query extension intro": "Enabling the question optimization function can improve the accuracy of Dataset searches during continuous conversations. After enabling this function, when performing Dataset searches, the AI will complete the missing information of the question based on the conversation history.", "core.dataset.Quote Length": "Quote Content Length", "core.dataset.Read Dataset": "View Dataset Details", "core.dataset.Set Website Config": "Start Configuring", "core.dataset.Start export": "Export Started", "core.dataset.Text collection": "Text Dataset", "core.dataset.apiFile": "API File", "core.dataset.collection.Click top config website": "Click to Configure Website", "core.dataset.collection.Collection raw text": "Dataset Content", "core.dataset.collection.Empty Tip": "The Dataset is Empty", "core.dataset.collection.QA Prompt": "QA Split Prompt", "core.dataset.collection.Start Sync Tip": "Confirm to Start Syncing Data? Old Data Will Be Deleted and Re-fetched, Please Confirm!", "core.dataset.collection.Sync": "Sync Data", "core.dataset.collection.Sync Collection": "Data Sync", "core.dataset.collection.Website Empty Tip": "No Website Associated Yet", "core.dataset.collection.Website Link": "Website Address", "core.dataset.collection.id": "Collection ID", "core.dataset.collection.metadata.Createtime": "Creation Time", "core.dataset.collection.metadata.Raw text length": "Raw Text Length", "core.dataset.collection.metadata.Updatetime": "Update Time", "core.dataset.collection.metadata.Web page selector": "Web Page Selector", "core.dataset.collection.metadata.metadata": "<PERSON><PERSON><PERSON>", "core.dataset.collection.metadata.read source": "View Original Content", "core.dataset.collection.metadata.source": "Data Source", "core.dataset.collection.metadata.source size": "Source Size", "core.dataset.collection.status.active": "Ready", "core.dataset.collection.status.error": "Error", "core.dataset.collection.sync.result.sameRaw": "Content Unchanged, No Update Needed", "core.dataset.collection.sync.result.success": "Sync Started", "core.dataset.data.Data Content": "Related Data Content", "core.dataset.data.Default Index Tip": "Cannot be edited. The default index will use the text of 'Related Data Content' and 'Auxiliary Data' to generate the index directly.", "core.dataset.data.Edit": "Edit Data", "core.dataset.data.Empty Tip": "This collection has no data yet", "core.dataset.data.Search data placeholder": "Search Related Data", "core.dataset.data.Too Long": "Total Length Exceeded", "core.dataset.data.Updated": "Updated", "core.dataset.data.group": "Role", "core.dataset.data.unit": "Items", "core.dataset.embedding model tip": "The index model can convert natural language into vectors for semantic search.\nNote that different index models cannot be used together. Once an index model is selected, it cannot be changed.", "core.dataset.error.Data not found": "Data Not Found or Deleted", "core.dataset.error.Start Sync Failed": "Failed to Start Sync", "core.dataset.error.invalidVectorModelOrQAModel": "Invalid Vector Model or QA Model", "core.dataset.error.unAuthDataset": "Unauthorized to Operate This Dataset", "core.dataset.error.unAuthDatasetCollection": "Unauthorized to Operate This Dataset", "core.dataset.error.unAuthDatasetData": "Unauthorized to Operate This Data", "core.dataset.error.unAuthDatasetFile": "Unauthorized to Operate This File", "core.dataset.error.unCreateCollection": "Unauthorized to Operate This Data", "core.dataset.error.unExistDataset": "The knowledge base does not exist", "core.dataset.error.unLinkCollection": "Not a Web Link Collection", "core.dataset.externalFile": "External File Library", "core.dataset.file": "File", "core.dataset.folder": "Directory", "core.dataset.import.Chunk Range": "Range: {{min}}~{{max}}", "core.dataset.import.Chunk Split Tip": "Segment the text according to certain rules and convert it into a format that can be semantically searched. Suitable for most scenarios. No additional model processing is required, and the cost is low.", "core.dataset.import.Continue upload": "Continue upload", "core.dataset.import.Custom prompt": "Custom Prompt", "core.dataset.import.Custom text": "Custom Text", "core.dataset.import.Custom text desc": "Manually enter a piece of text as a dataset", "core.dataset.import.Data process params": "Data Processing Parameters", "core.dataset.import.Down load csv template": "Click to Download CSV Template", "core.dataset.import.Link name": "Web Link", "core.dataset.import.Link name placeholder": "Only supports static links. If the data is empty after uploading, the link may not be readable\nEach line one, up to 10 links at a time", "core.dataset.import.Local file": "Local File", "core.dataset.import.Local file desc": "Upload files in PDF, TXT, DOCX, etc. formats", "core.dataset.import.Preview chunks": "Preview Chunks (limit 15)", "core.dataset.import.Preview raw text": "Preview Raw Text (up to 3000 characters)", "core.dataset.import.Process way": "Processing Method", "core.dataset.import.QA Import": "QA Split", "core.dataset.import.QA Import Tip": "According to certain rules, split the text into larger paragraphs and call AI to generate Q&A pairs for the paragraph. It has very high retrieval accuracy but may lose a lot of content details.", "core.dataset.import.Select file": "Select File", "core.dataset.import.Select source": "Select Source", "core.dataset.import.Source name": "Source Name", "core.dataset.import.Sources list": "Sources", "core.dataset.import.Start upload": "Start Upload", "core.dataset.import.Upload complete": "Upload complete", "core.dataset.import.Upload data": "Confirm Upload", "core.dataset.import.Upload file progress": "File Upload Progress", "core.dataset.import.Upload status": "Status", "core.dataset.import.Web link": "Web Link", "core.dataset.import.Web link desc": "Read static web page content as a dataset", "core.dataset.import.import_success": "Import Successful, Please Wait for Training", "core.dataset.link": "Link", "core.dataset.search.Dataset Search Params": "Dataset Search Configuration", "core.dataset.search.Empty result response": "Empty Search Response", "core.dataset.search.Filter": "Search Filter", "core.dataset.search.No support similarity": "Only supported when using result re-rank or semantic search", "core.dataset.search.Nonsupport": "Not Supported", "core.dataset.search.Params Setting": "Search Parameter Settings", "core.dataset.search.Quote index": "Quote Index", "core.dataset.search.ReRank": "Result Re-rank", "core.dataset.search.ReRank desc": "Use the re-rank model for secondary sorting to enhance the comprehensive ranking.", "core.dataset.search.Source id": "Source ID", "core.dataset.search.Source index": "What sources", "core.dataset.search.Source name": "Quote Source Name", "core.dataset.search.Using query extension": "Use Question Optimization", "core.dataset.search.mode.embedding": "Semantic Search", "core.dataset.search.mode.embedding desc": "Use vectors for text relevance queries", "core.dataset.search.mode.fullTextRecall": "Full Text Search", "core.dataset.search.mode.fullTextRecall desc": "Use traditional full-text search, suitable for finding some keywords and subject-predicate special data", "core.dataset.search.mode.mixedRecall": "Mixed Search", "core.dataset.search.mode.mixedRecall desc": "Use a combination of vector search and full-text search results, sorted using the RRF algorithm.", "core.dataset.search.score.embedding desc": "Get scores by calculating the distance between vectors, ranging from 0 to 1.", "core.dataset.search.score.fullText": "Full Text Search", "core.dataset.search.score.fullText desc": "Calculate the score of the same keywords, ranging from 0 to infinity.", "core.dataset.search.score.reRank": "Result Re-rank", "core.dataset.search.score.reRank desc": "Calculate the relevance between sentences using the re-rank model, ranging from 0 to 1.", "core.dataset.search.score.rrf": "Comprehensive Ranking", "core.dataset.search.score.rrf desc": "Merge multiple search results using the reciprocal rank fusion method.", "core.dataset.search.search mode": "Search Method", "core.dataset.status.active": "Ready", "core.dataset.status.syncing": "Syncing", "core.dataset.status.waiting": "Waiting", "core.dataset.test.Batch test": "Batch Test", "core.dataset.test.Batch test Placeholder": "Select a CSV File", "core.dataset.test.Search Test": "Search Test", "core.dataset.test.Test": "Test", "core.dataset.test.Test Result": "Test Result", "core.dataset.test.Test Text": "Single Text Test", "core.dataset.test.Test Text Placeholder": "Enter the text to be tested", "core.dataset.test.Test params": "Test Parameters", "core.dataset.test.delete test history": "Delete This Test Result", "core.dataset.test.test history": "Test History", "core.dataset.test.test result placeholder": "Test results will be displayed here", "core.dataset.test.test result tip": "Sort based on the similarity between the Dataset content and the test text. You can adjust the corresponding text based on the test results.\nNote: The data in the test records may have been modified. Clicking on a test data will display the latest data.", "core.dataset.training.Agent queue": "QA Training Queue", "core.dataset.training.Auto mode": "Auto index", "core.dataset.training.Auto mode Tip": "Increase the semantic richness of data blocks by generating related questions and summaries through sub-indexes and calling models, making it more conducive to retrieval. Requires more storage space and increases AI call times.", "core.dataset.training.Chunk mode": "Chunk", "core.dataset.training.Full": "It is expected to be more than 20 minutes", "core.dataset.training.Leisure": "Idle", "core.dataset.training.QA mode": "QA", "core.dataset.training.Vector queue": "Index Queue", "core.dataset.training.Waiting": "Estimated 20 minutes", "core.dataset.training.Website Sync": "Website Sync", "core.dataset.training.tag": "Queue Status", "core.dataset.website.Base Url": "Base URL", "core.dataset.website.Config": "Website Configuration", "core.dataset.website.Config Description": "The website sync function allows you to fill in the root address of a website. The system will automatically crawl related web pages for Dataset training. Only static websites will be crawled, mainly project documentation and blogs.", "core.dataset.website.Confirm Create Tips": "Confirm to sync this site. The sync task will start shortly. Please confirm!", "core.dataset.website.Confirm Update Tips": "Confirm to update the site configuration? The sync will start immediately according to the new configuration. Please confirm!", "core.dataset.website.Selector": "Selector", "core.dataset.website.Selector Course": "Usage Tutorial", "core.dataset.website.Start Sync": "Start Sync", "core.dataset.website.UnValid Website Tip": "Your site may not be a static site and cannot be synced", "core.module.Add question type": "Add Question Type", "core.module.Add_option": "Add Option", "core.module.Can not connect self": "Cannot Connect to Itself", "core.module.Data Type": "Data Type", "core.module.Dataset quote.label": "Dataset Quote", "core.module.Dataset quote.select": "Select Dataset Quote", "core.module.Default Value": "Default Value", "core.module.Default value": "Default Value", "core.module.Default value placeholder": "Leave blank to return an empty string by default", "core.module.Diagram": "Diagram", "core.module.Edit intro": "Edit Description", "core.module.Field Description": "Field Description", "core.module.Field Name": "Field Name", "core.module.Http request props": "Request Parameters", "core.module.Http request settings": "Request Configuration", "core.module.Http timeout": "Timeout Duration", "core.module.Input Type": "Input Type", "core.module.Laf sync params": "Sync Parameters", "core.module.Max Length": "Max Length", "core.module.Max Length placeholder": "Maximum length of input text", "core.module.Max Value": "Max Value", "core.module.Min Value": "Min Value", "core.module.QueryExtension.placeholder": "For example:\nQuestions about the introduction and use of Python.\nThe current conversation is related to the game 'GTA5'.", "core.module.Select app": "Select App", "core.module.Setting quote prompt": "Configure Quote Prompt", "core.module.Variable": "Global Variable", "core.module.Variable Setting": "Variable Setting", "core.module.edit.Field Name Cannot Be Empty": "Field Name Cannot Be Empty", "core.module.edit.Field Value Type Cannot Be Empty": "Optional data type cannot be empty.", "core.module.extract.Add field": "Add Field", "core.module.extract.Enum Description": "List the possible values of this field, one per line", "core.module.extract.Enum Value": "Enum Value", "core.module.extract.Field Description Placeholder": "Name/Age/SQL Statement...", "core.module.extract.Field Setting Title": "Extract Field Configuration", "core.module.extract.Required": "Must Return", "core.module.extract.Required Description": "Even if the field cannot be extracted, it will be returned using the default value", "core.module.extract.Target field": "Target Field", "core.module.http.Add props": "Add Parameter", "core.module.http.AppId": "App ID", "core.module.http.ChatId": "Current Chat ID", "core.module.http.Current time": "Current Time", "core.module.http.Histories": "History Records", "core.module.http.Key already exists": "Key Already Exists", "core.module.http.Key cannot be empty": "Parameter Name Cannot Be Empty", "core.module.http.Props name": "Parameter Name", "core.module.http.Props tip": "You can set related parameters for the HTTP request\nYou can call global variables or external parameter inputs through {{key}}, currently available variables:\n{{variable}}", "core.module.http.Props value": "Parameter Value", "core.module.http.ResponseChatItemId": "AI Response ID", "core.module.http.Url and params have been split": "Path parameters have been automatically added to Params", "core.module.http.curl import": "cURL Import", "core.module.http.curl import placeholder": "Please enter the cURL format content, the request information of the first interface will be extracted.", "core.module.input.Add Branch": "Add Branch", "core.module.input.add": "Add Condition", "core.module.input.description.Background": "You can add some specific content introductions to better identify the type of user questions. This content is usually to introduce something the model does not know.", "core.module.input.description.HTTP Dynamic Input": "Receive the output value of the previous node as a variable, which can be used by the HTTP request parameters.", "core.module.input.description.Http Request Header": "Custom request headers, please strictly fill in the JSON string.\n1. Ensure that the last attribute has no comma\n2. Ensure that the key contains double quotes\nFor example: {\"Authorization\":\"Bearer xxx\"}", "core.module.input.description.Http Request Url": "New HTTP request address. If there are two 'request addresses', you can delete this module and re-add it to pull the latest module configuration.", "core.module.input.description.Response content": "You can use \\n to achieve continuous line breaks.\nYou can achieve replies through external module input, and the content filled in here will be overwritten by external module input.\nIf non-string type data is passed in, it will be automatically converted to a string", "core.module.input.label.Background": "Background Knowledge", "core.module.input.label.Http Request Url": "Request Address", "core.module.input.label.Response content": "Response Content", "core.module.input.label.Select dataset": "Select Dataset", "core.module.input.label.aiModel": "AI Model", "core.module.input.label.chat history": "Chat History", "core.module.input.label.user question": "User Question", "core.module.input.placeholder.Classify background": "For example:\n1. AIGC (Artificial Intelligence Generated Content) refers to the use of artificial intelligence technology to automatically or semi-automatically generate digital content, such as text, images, music, videos, etc.\n2. AIGC technology includes but is not limited to natural language processing, computer vision, machine learning, and deep learning. These technologies can create new content or modify existing content to meet specific creative, educational, entertainment, or informational needs.", "core.module.input_description": "Description", "core.module.input_form": "Input form", "core.module.input_name": "input_name", "core.module.input_type": "Input type", "core.module.laf.Select laf function": "Select LAF Function", "core.module.output.description.Ai response content": "Will be triggered after the stream reply is completed", "core.module.output.description.New context": "Splice the current reply content with the history records and return it as the new context", "core.module.output.description.query extension result": "Output as a string array, which can be directly connected to the 'User Question' of 'Dataset Search'. It is recommended not to connect to the 'User Question' of 'AI Chat'", "core.module.output.label.Ai response content": "AI Response Content", "core.module.output.label.New context": "New Context", "core.module.output.label.query extension result": "Optimization Result", "core.module.template.AI function": "AI Capability", "core.module.template.AI response switch tip": "If you want the current node not to output content, you can turn off this switch. The content output by AI will not be displayed to the user, and you can manually use 'AI Response Content' for special processing.", "core.module.template.AI support tool tip": "Models that support function calls can better use tool calls.", "core.module.template.Basic Node": "Basic", "core.module.template.Query extension": "Question Optimization", "core.module.template.System Plugin": "System Plugin", "core.module.template.System input module": "System Input", "core.module.template.Team app": "Team", "core.module.template.Tool module": "Tool", "core.module.template.UnKnow Module": "Unknown <PERSON><PERSON>le", "core.module.template.ai_chat": "AI conversation", "core.module.template.ai_chat_intro": "AI large model dialogue", "core.module.template.config_params": "Can configure application system parameters", "core.module.template.empty_plugin": "Blank plugin", "core.module.template.empty_workflow": "Blank workflow", "core.module.template.self_input": "Plug-in input", "core.module.template.self_output": "Custom plug-in output", "core.module.template.system_config": "System configuration", "core.module.template.system_config_info": "Can configure application system parameters", "core.module.template.work_start": "Process starts", "core.module.templates.Load plugin error": "Failed to Load Plugin", "core.module.variable add option": "Add Option", "core.module.variable.Custom type": "Custom Variable", "core.module.variable.add option": "Add Option", "core.module.variable.input type": "Text", "core.module.variable.key": "Variable Key", "core.module.variable.key already exists": "Key Already Exists", "core.module.variable.key is required": "Variable Key is Required", "core.module.variable.select type": "Dropdown Single Select", "core.module.variable.text max length": "Max Length", "core.module.variable.textarea type": "Paragraph", "core.module.variable.variable name is required": "Variable Name Cannot Be Empty", "core.module.variable.variable option is required": "Options Cannot Be All Empty", "core.module.variable.variable option is value is required": "Option Content Cannot Be Empty", "core.module.variable.variable options": "Options", "core.plugin.Custom headers": "Custom Request Headers", "core.plugin.Free": "This plugin does not consume points", "core.plugin.Get Plugin Module Detail Failed": "Failed to Retrieve Plugin Information", "core.plugin.Http plugin intro placeholder": "For display only, no actual effect", "core.plugin.cost": "Points Consumption:", "core.tip.leave page": "Content has been modified, confirm to leave the page?", "core.view_chat_detail": "View Chat Details", "core.workflow.Can not delete node": "This Node Cannot Be Deleted", "core.workflow.Change input type tip": "Changing the input type will clear the filled values, please confirm!", "core.workflow.Check Failed": "Workflow verification failed, please check whether the value is missing, and whether the connection is normal.", "core.workflow.Confirm stop debug": "Confirm to Stop Debugging? Debug Information Will Not Be Retained.", "core.workflow.Copy node": "Node Copied", "core.workflow.Custom inputs": "Custom Inputs", "core.workflow.Custom outputs": "Custom Outputs", "core.workflow.Dataset quote": "Dataset Quote", "core.workflow.Debug": "Debug", "core.workflow.Debug Node": "Debug Mode", "core.workflow.Failed": "Run Failed", "core.workflow.Not intro": "This Node Has No Introduction", "core.workflow.Run": "Run", "core.workflow.Running": "Running", "core.workflow.Save and publish": "Save and Publish", "core.workflow.Save to cloud": "Save Only", "core.workflow.Skipped": "Skipped", "core.workflow.Stop debug": "Stop Debugging", "core.workflow.Success": "Run Successful", "core.workflow.Value type": "Data Type", "core.workflow.debug.Done": "Debugging Completed", "core.workflow.debug.Hide result": "<PERSON><PERSON> Result", "core.workflow.debug.Not result": "No Run Result", "core.workflow.debug.Run result": "<PERSON> Result", "core.workflow.debug.Show result": "Show Result", "core.workflow.dynamic_input": "dynamic input", "core.workflow.inputType.JSON Editor": "JSON Input Box", "core.workflow.inputType.Manual input": "Manual Input", "core.workflow.inputType.Manual select": "Manual Select", "core.workflow.inputType.Reference": "Variable Reference", "core.workflow.inputType.custom": "Custom Variable", "core.workflow.inputType.dynamicTargetInput": "Dynamic External Data", "core.workflow.inputType.input": "Single Line Input Box", "core.workflow.inputType.number input": "Number Input Box", "core.workflow.inputType.select": "Single Select Box", "core.workflow.inputType.selectApp": "App Select", "core.workflow.inputType.selectDataset": "Dataset Select", "core.workflow.inputType.selectLLMModel": "Chat Model Select", "core.workflow.inputType.switch": "Switch", "core.workflow.inputType.textInput": "Text Input box", "core.workflow.inputType.textarea": "Multi-line Input Box", "core.workflow.publish.OnRevert version": "Click to Revert to This Version", "core.workflow.publish.OnRevert version confirm": "Confirm to Revert to This Version? The configuration of the editing version will be saved, and a new release version will be created for the reverted version.", "core.workflow.publish.histories": "Release Records", "core.workflow.template.Interactive": "Interactive", "core.workflow.template.Multimodal": "Multimodal", "core.workflow.template.Search": "Search", "core.workflow.tool.Handle": "Tool Connector", "core.workflow.tool.Select Tool": "Select Tool", "core.workflow.variable": "Variable", "create": "Create", "create_failed": "Create failed", "create_success": "Created Successfully", "create_time": "Creation Time", "cron_job_run_app": "Scheduled Task", "custom_title": "Custom Title", "data_index_custom": "Custom index", "data_index_default": "Default index", "data_index_question": "Inferred question index", "data_index_summary": "Summary Index", "data_not_found": "Data can't be found", "dataset.Confirm move the folder": "Confirm to Move to This Directory", "dataset.Confirm to delete the data": "Confirm to Delete This Data?", "dataset.Confirm to delete the file": "Confirm to Delete This File and All Its Data?", "dataset.Create Folder": "Create Folder", "dataset.Create manual collection": "Create Manual Dataset", "dataset.Delete Dataset Error": "Delete Dataset Error", "dataset.Edit Folder": "Edit <PERSON>", "dataset.Edit Info": "Edit Information", "dataset.Export": "Export", "dataset.Export Dataset Limit Error": "Export Data Failed", "dataset.Folder Name": "Enter Folder Name", "dataset.Insert Data": "Insert", "dataset.Manual collection Tip": "Manual datasets allow you to create an empty container to hold data", "dataset.Move Failed": "Move Error", "dataset.Select Dataset": "Select This Dataset", "dataset.Select Dataset Tips": "Only Datasets with the same index model can be selected", "dataset.Select Folder": "Enter Folder", "dataset.Training Name": "Data Training", "dataset.collections.Collection Embedding": "{{total}} Indexes", "dataset.collections.Confirm to delete the folder": "Confirm to Delete This Folder and All Its Contents?", "dataset.collections.Create And Import": "Create/Import", "dataset.collections.Select Collection": "Select File", "dataset.collections.Select One Collection To Store": "Select a File to Store", "dataset.data.Can not edit": "No Edit Permission", "dataset.data.Default Index": "Default Index", "dataset.data.Delete Tip": "Confirm to Delete This Data?", "dataset.data.Index Placeholder": "Enter Index Text Content", "dataset.data.Input Success Tip": "Data Imported Successfully", "dataset.data.Update Success Tip": "Data Updated Successfully", "dataset.data.edit.Index": "Data Index ({{amount}})", "dataset.data.edit.divide_content": "Segment Content", "dataset.data.input is empty": "Data Content Cannot Be Empty", "dataset.dataset_name": "Dataset Name", "dataset.deleteFolderTips": "Confirm to Delete This Folder and All Its Contained Datasets? Data Cannot Be Recovered After Deletion, Please Confirm!", "dataset.test.noResult": "No Search Results", "dataset_data_input_a": "Answer", "dataset_data_input_chunk": "Chunk", "dataset_data_input_chunk_content": "Chunk", "dataset_data_input_q": "question", "dataset_data_input_qa": "QA", "dataset_text_model_tip": "Used for text processing in the knowledge base preprocessing stage, such as automatic supplementary indexing, Q&A pair extraction.", "deep_rag_search": "In-depth search", "delete_api": "Are you sure you want to delete this API key? \nAfter deletion, the key will become invalid immediately and the corresponding conversation log will not be deleted. Please confirm!", "delete_failed": "Deletion Failed", "delete_folder": "Delete Folder", "delete_success": "Deleted Successfully", "delete_warning": "Deletion Warning", "embedding_model_not_config": "No index model is detected", "enable_auth": "Enable authentication", "error.Create failed": "Create failed", "error.code_error": "Verification code error", "error.fileNotFound": "File not found~", "error.inheritPermissionError": "Inherit permission Error", "error.invalid_params": "Invalid parameter", "error.missingParams": "Insufficient parameters", "error.send_auth_code_too_frequently": "Please do not obtain verification code frequently", "error.too_many_request": "Too many request", "error.unKnow": "An Unexpected Error Occurred", "error.upload_file_error_filename": "{{name}} Upload Failed", "error.upload_image_error": "File upload failed", "error.username_empty": "Account cannot be empty", "error_collection_not_exist": "The collection does not exist", "error_embedding_not_config": "Unconfigured index model", "error_invalid_resource": "Invalid resources", "error_llm_not_config": "Unconfigured file understanding model", "error_un_permission": "No permission to operate", "error_vlm_not_config": "Image comprehension model not configured", "exit_directly": "exit_directly", "expired_time": "Expiration Time", "export_to_json": "Export to JSON", "extraction_results": "Extraction Results", "failed": "Failed", "field_name": "Field Name", "folder.empty": "No More Items in This Directory", "folder.open_dataset": "Open Dataset", "folder_description": "Folder Description", "free": "Free", "get_QR_failed": "Failed to Get QR Code", "get_app_failed": "Failed to Retrieve App", "get_laf_failed": "Failed to Retrieve Laf Function List", "had_auth_value": "Filled in", "has_verification": "Verified, Click to Unbind", "have_done": "Completed", "import_failed": "Import Failed", "import_success": "Imported Successfully", "info.buy_extra": "Buy Extra Package", "info.csv_download": "Click to Download Batch Test Template", "info.csv_message": "Read the first column of the CSV file for batch testing, supporting up to 100 roles of data at a time.", "info.felid_message": "Field key must be pure English letters or numbers and cannot start with a number.", "info.free_plan": "If a free team does not log in to the system for 30 consecutive days, the system will automatically clear the account's Dataset.", "info.include": "Includes Standard Package and Extra Resource Pack", "info.node_info": "Adjusting this module will affect the timing of tool calls.\nYou can guide the model to call tools by accurately describing the function of this module.", "info.old_version_attention": "Detected that your advanced orchestration is an old version. The system will automatically format it into the new workflow version.\n\nDue to significant version differences, some workflows may not be arranged correctly. Please manually reconnect the workflow. If it is still abnormal, try deleting the corresponding node and re-adding it.\n\nYou can directly click debug to test the workflow. After debugging, click publish. The new workflow will only be saved and take effect after you click publish.\n\nBefore you publish the new workflow, auto-save will not take effect.", "info.open_api_notice": "You can fill in the relevant keys of OpenAI/OneAPI. If you fill in this content, the 'AI Chat', 'Question Classification', and 'Content Extraction' on the online platform will use the key you filled in and will not be charged. Please check if your key has access to the corresponding model. GPT models can choose FastAI.", "info.open_api_placeholder": "Request address, default is the official OpenAI. You can fill in the transit address, 'v1' will not be automatically completed", "info.resource": "Resource Usage", "input.Repeat Value": "Duplicate Value", "input_name": "Enter a Name", "invalid_variable": "Invalid Variable", "is_open": "Is Open", "is_requesting": "Requesting...", "is_using": "In Use", "item_description": "Field Description", "item_name": "Field Name", "json_config": "JSON Configuration", "json_parse_error": "Possible JSON Error, Please Check Carefully", "just_now": "just", "key": "key", "key_repetition": "Key Repetition", "last_step": "Previous", "last_use_time": "Last Use Time", "link.UnValid": "Invalid Link", "llm_model_not_config": "No language model was detected", "load_failed": "load_failed", "max_quote_tokens": "Quote cap", "max_quote_tokens_tips": "The maximum number of tokens in a single search, about 1 character in Chinese = 1.7 tokens, and about 1 character in English = 1 token", "mcp_server": "MCP Services", "min_similarity": "lowest correlation", "min_similarity_tip": "The relevance of different index models is different. Please select the appropriate value through search testing. \nWhen using Result Rearrange , use the rearranged results for filtering.", "model.billing": "Billing", "model.model_type": "Model type", "model.name": "Model name", "model.provider": "Provider", "model.search_name_placeholder": "Search by model name", "model.type.chat": "LLM", "model.type.embedding": "Embedding", "model.type.reRank": "ReRank", "model.type.stt": "STT", "model.type.tts": "TTS", "model_alicloud": "<PERSON>", "model_baai": "BAAI", "model_baichuan": "Baichuan", "model_chatglm": "ChatGLM", "model_doubao": "Do<PERSON><PERSON>", "model_ernie": "<PERSON>", "model_hunyuan": "Hunyuan", "model_intern": "Intern", "model_moka": "Moka-AI", "model_moonshot": "Moonshot", "model_other": "Other", "model_ppio": "PPIO", "model_qwen": "<PERSON><PERSON>", "model_siliconflow": "Siliconflow", "model_sparkdesk": "SprkDesk", "model_stepfun": "<PERSON><PERSON><PERSON>", "model_yi": "<PERSON>", "month": "Month", "move.confirm": "Confirm move", "move_success": "Moved Successfully", "move_to": "Move to", "name": "name", "name_is_empty": "Name Cannot Be Empty", "navbar.Account": "Account", "navbar.Chat": "Cha<PERSON>", "navbar.Datasets": "Dataset", "navbar.Studio": "Studio", "navbar.Toolkit": "<PERSON><PERSON><PERSON>", "navbar.Tools": "Tools", "new_create": "Create New", "next_step": "Next", "no": "No", "no_child_folder": "No Subdirectories, Place Here", "no_intro": "No Introduction Available", "no_laf_env": "System Not Configured with Laf Environment", "no_more_data": "No More Data", "no_pay_way": "There is no suitable payment channel in the system", "no_select_data": "No Data Available", "not_model_config": "No related model configured", "not_open": "Not Open", "not_permission": "The current subscription package does not support team operation logs", "not_support": "Not Supported", "not_support_wechat_image": "This is a WeChat picture", "not_yet_introduced": "No Introduction Yet", "open_folder": "Open Folder", "option": "Option", "page_center": "Page Center", "pay.amount": "Amount", "pay.error_desc": "There was a problem when converting payment routes", "pay.noclose": "After payment is completed, please wait for the system to update automatically", "pay.package_tip.buy": "The package you purchased is lower than the current package. This package will take effect after the current package expires.\nYou can view the package usage in Account - Personal Information - Package Details.", "pay.package_tip.renewal": "You are renewing the package. You can view the package usage in Account - Personal Information - Package Details.", "pay.package_tip.upgrade": "The package you purchased is higher than the current package. This package will take effect immediately, and the current package will take effect later. You can view the package usage in Account - Personal Information - Package Details.", "pay.wechat": "Please scan the QR code on WeChat to pay: {{price}} yuan\n\nPlease do not close the page before payment is completed", "pay.wx_payment": "WeChat Payment", "pay.yuan": "{{amount}} Yuan", "pay_alipay_payment": "Alipay Payment", "pay_corporate_payment": "Payment to the public", "pay_money": "Amount payable", "pay_success": "Payment successfully", "pay_year_tip": "Pay 10 months, enjoy 1 year!", "permission.Collaborator": "Collaborator", "permission.Default permission": "Default Permission", "permission.Manage": "Manage", "permission.No InheritPermission": "Permission Inheritance Restricted", "permission.Not collaborator": "No Collaborator", "permission.Owner": "Owner", "permission.Permission": "Permission", "permission.Permission config": "Permission Configuration", "permission.Private": "Private", "permission.Private Tip": "Only Available to Yourself", "permission.Public": "Team", "permission.Public Tip": "Available to All Team Employees", "permission.Remove InheritPermission Confirm": "This operation will invalidate permission inheritance. Proceed?", "permission.Resume InheritPermission Confirm": "Resume inheriting permissions from the parent folder?", "permission.Resume InheritPermission Failed": "Resume Failed", "permission.Resume InheritPermission Success": "Resume Successful", "permission.change_owner": "Transfer Ownership", "permission.change_owner_failed": "Transfer Ownership Failed", "permission.change_owner_placeholder": "<PERSON><PERSON> to Search Account", "permission.change_owner_success": "Ownership Transferred Successfully", "permission.change_owner_tip": "Your permissions will not be retained after the transfer", "permission.change_owner_to": "Transfer to", "permission.manager": "administrator", "permission.read": "Read permission", "permission.write": "write permission", "please_input_name": "Please Enter a Name", "plugin.App": "Select App", "plugin.Currentapp": "Current App", "plugin.Description": "Description", "plugin.Edit Http Plugin": "Edit HTTP Plugin", "plugin.Enter PAT": "Enter Personal Access Token (PAT)", "plugin.Get Plugin Module Detail Failed": "Failed to Retrieve Plugin Information", "plugin.Import Plugin": "Import HTTP Plugin", "plugin.Import from URL": "Import from URL. https://xxxx", "plugin.Intro": "Plugin Introduction", "plugin.Invalid Env": "Invalid Laf Environment", "plugin.Invalid Schema": "<PERSON><PERSON><PERSON>", "plugin.Invalid URL": "Invalid URL", "plugin.Method": "Method", "plugin.Path": "Path", "plugin.Please bind laf accout first": "Please Bind <PERSON><PERSON> Account First", "plugin.Plugin List": "Plugin List", "plugin.Search plugin": "Search Plugin", "plugin.Search_app": "Search App", "plugin.Set Name": "Name the Plugin", "plugin.contribute": "Contribute Plugin", "plugin.go to laf": "Go to Write", "plugin.path": "Path", "price_over_wx_limit": "Exceed payment provider limit: WeChat Pay only supports less than 6,000 yuan", "prompt_input_placeholder": "Please enter the prompt word", "psw_inconsistency": "Passwords Do Not Match", "question_feedback": "Work order", "read_course": "Read Course", "read_doc": "Read Document", "read_quote": "View citations", "redo_tip": "Redo  ctrl  shift  z", "redo_tip_mac": "Redo  ⌘  shift  z", "request_end": "All Loaded", "request_error": "request_error", "request_more": "Click to Load More", "required": "Required", "rerank_weight": "Rearrange weights", "resume_failed": "Resume Failed", "root_folder": "Root Folder", "save_failed": "save_failed", "save_success": "Saved Successfully", "scan_code": "Scan the QR code to pay", "secret_tips": "The value will not return plaintext again after saving", "select_file_failed": "File Selection Failed", "select_reference_variable": "Select Reference Variable", "select_template": "Select Template", "set_avatar": "Click to set_avatar", "share_link": "Share Link", "speech_error_tip": "Speech to Text Failed", "speech_not_support": "Your Browser Does Not Support Speech Input", "submit_failed": "Submission Failed", "submit_success": "Submitted Successfully", "submitted": "Submitted", "support": "Support", "support.account.Individuation": "Personalization", "support.inform.Read": "Read", "support.openapi.Api baseurl": "API Base URL", "support.openapi.Api manager": "API Key Management", "support.openapi.Copy success": "API Address Copied", "support.openapi.New api key": "New API Key", "support.openapi.New api key tip": "Please keep your key safe, it will not be displayed again", "support.outlink.Delete link tip": "Confirm to Delete This Login-Free Link? The link will become invalid immediately after deletion, but the chat logs will be retained. Please confirm!", "support.outlink.Max usage points": "Points Limit", "support.outlink.Max usage points tip": "The maximum number of points allowed for this link. It cannot be used after exceeding the limit. -1 means unlimited.", "support.outlink.Usage points": "Points Consumption", "support.outlink.share.Chat_quote_reader": "Full text reader", "support.outlink.share.Full_text tips": "Allows reading of the complete dataset from which the referenced fragment is derived", "support.outlink.share.Response Quote": "Return Quote", "support.outlink.share.Response Quote tips": "Return quoted content in the share link, but do not allow users to download the original document", "support.outlink.share.running_node": "Running node", "support.outlink.share.show_complete_quote": "View original source", "support.outlink.share.show_complete_quote_tips": "View and download the complete citation document, or jump to the citation website", "support.permission.Permission": "Permission", "support.standard.AI Bonus Points": "AI Points", "support.standard.due_date": "Due Date", "support.standard.storage": "Storage", "support.standard.type": "Type", "support.team.limit.No permission rerank": "No Permission to Use Result Re-rank, Please Upgrade Your Package", "support.user.Avatar": "Avatar", "support.user.Go laf env": "Click to Go to {{env}} to Get PAT Token.", "support.user.Laf account course": "View the Tutorial for Binding Laf Account.", "support.user.Laf account intro": "After binding your Laf account, you can use the Laf module in the workflow to write code online.", "support.user.Need to login": "Please Log In First", "support.user.Price": "Pricing", "support.user.User self info": "Profile", "support.user.auth.Sending Code": "Sending Code", "support.user.auth.get_code": "Get Verification Code", "support.user.auth.get_code_again": "s Get Again", "support.user.captcha_placeholder": "Please enter the verification code", "support.user.info.bind_notification_error": "Abnormal binding notification account", "support.user.info.bind_notification_hint": "Please bind the notification receiving account to ensure that you can receive notifications such as package expiration reminders, etc., to ensure the normal operation of your service.", "support.user.info.bind_notification_success": "Binding notification account successful", "support.user.info.code_required": "Verification code cannot be empty", "support.user.info.notification_receiving_hint": "Notification reception", "support.user.info.verification_code": "Verification Code", "support.user.inform.System message": "System Message", "support.user.login.Email": "Email", "support.user.login.Github": "<PERSON><PERSON><PERSON><PERSON>", "support.user.login.Google": "Google Login", "support.user.login.Microsoft": "Microsoft Login", "support.user.login.Password": "Password", "support.user.login.Password login": "Password Login", "support.user.login.Phone": "Phone Login", "support.user.login.Phone number": "Phone Number", "support.user.login.Provider error": "<PERSON><PERSON>, Please Try Again", "support.user.login.Username": "Username", "support.user.login.Wechat": "<PERSON><PERSON><PERSON>", "support.user.login.can_not_login": "Cannot log in? Click here to contact us", "support.user.login.error": "<PERSON><PERSON>", "support.user.login.security_failed": "Security Verification Failed", "support.user.login.wx_qr_login": "WeChat QR Code Login", "support.user.logout.confirm": "Confirm to Log Out?", "support.user.team.Dataset usage": "Dataset Capacity", "support.user.team.Team Tags Async Success": "Sync Completed", "support.user.team.member": "Employee", "support.wallet.Ai point every thousand tokens": "{{points}} Points/1K Tokens", "support.wallet.Ai point every thousand tokens_input": "Input：{{points}} points/1K tokens", "support.wallet.Ai point every thousand tokens_output": "Output：{{points}} points/1K tokens", "support.wallet.Amount": "Amount", "support.wallet.App_amount_not_sufficient": "The number of your applications has reached the limit. Please upgrade your plan to continue using.", "support.wallet.Buy": "Buy", "support.wallet.Dataset_amount_not_sufficient": "The number of your datasets has reached the limit. Please upgrade your plan to continue using.", "support.wallet.Dataset_not_sufficient": "Your dataset capacity is insufficient. Please upgrade your plan or purchase additional dataset capacity to continue using.", "support.wallet.Not sufficient": "Insufficient AI Points, Please Upgrade Your Package or Purchase Additional AI Points to Continue Using.", "support.wallet.Plan expired time": "Package Expiration Time", "support.wallet.Standard Plan Detail": "Package Details", "support.wallet.Team_member_over_size": "The number of your team members has reached the limit. Please upgrade your plan to continue using.", "support.wallet.To read plan": "View Package", "support.wallet.amount_0": "Purchase Quantity Cannot Be 0", "support.wallet.apply_invoice": "Apply for Invoice", "support.wallet.bill.Number": "Order Number", "support.wallet.bill.Status": "Status", "support.wallet.bill.Type": "Order Type", "support.wallet.bill.payWay.Way": "Payment Method", "support.wallet.bill.payWay.alipay": "Alipay Payment", "support.wallet.bill.payWay.balance": "Balance Payment", "support.wallet.bill.payWay.bank": "Bank Transfer", "support.wallet.bill.payWay.wx": "WeChat Payment", "support.wallet.bill.status.closed": "Closed", "support.wallet.bill.status.notpay": "Unpaid", "support.wallet.bill.status.refund": "Refunded", "support.wallet.bill.status.success": "Payment Successful", "support.wallet.bill_detail": "<PERSON>", "support.wallet.bill_tag.bill": "Bill Records", "support.wallet.bill_tag.default_header": "<PERSON><PERSON><PERSON>", "support.wallet.bill_tag.invoice": "Invoice Records", "support.wallet.billable_invoice": "Billable Invoice", "support.wallet.buy_resource": "Buy Resource Pack", "support.wallet.has_invoice": "Invoiced", "support.wallet.invoice_amount": "Invoice Amount", "support.wallet.invoice_data.bank": "Bank", "support.wallet.invoice_data.bank_account": "Bank Account", "support.wallet.invoice_data.company_address": "Company Address", "support.wallet.invoice_data.company_phone": "Company Phone", "support.wallet.invoice_data.email": "Email Address", "support.wallet.invoice_data.need_special_invoice": "Need Special Invoice", "support.wallet.invoice_data.organization_name": "Organization Name", "support.wallet.invoice_data.unit_code": "Unified Credit Code", "support.wallet.invoice_detail": "Invoice Details", "support.wallet.invoice_info": "The invoice will be sent to the email within 3-7 working days, please wait patiently", "support.wallet.invoicing": "Invoicing", "support.wallet.moduleName.qa": "QA Split", "support.wallet.noBill": "No Bill Records", "support.wallet.no_invoice": "No Invoice Records", "support.wallet.subscription.AI points": "AI Points", "support.wallet.subscription.AI points click to read tip": "Each time the AI model is called, a certain amount of AI points (similar to tokens) will be consumed. Click to view detailed calculation rules.", "support.wallet.subscription.AI points usage": "AI Points Usage", "support.wallet.subscription.AI points usage tip": "Each time the AI model is called, a certain amount of AI points will be consumed. For specific calculation standards, please refer to the 'Pricing' above.", "support.wallet.subscription.Ai points": "AI Points Calculation Standards", "support.wallet.subscription.Current plan": "Current Package", "support.wallet.subscription.Extra ai points": "AI points", "support.wallet.subscription.Extra dataset size": "Extra Dataset Capacity", "support.wallet.subscription.Extra plan": "Extra Resource Pack", "support.wallet.subscription.Extra plan tip": "When the standard package is not enough, you can purchase extra resource packs to continue using", "support.wallet.subscription.FAQ": "FAQ", "support.wallet.subscription.Month amount": "Months", "support.wallet.subscription.Next plan": "Future Package", "support.wallet.subscription.Stand plan level": "Subscription Package", "support.wallet.subscription.Sub plan": "Subscription Package", "support.wallet.subscription.Sub plan tip": "Free to use [{{title}}] or upgrade to a higher package", "support.wallet.subscription.Team plan and usage": "Package and Usage", "support.wallet.subscription.Training weight": "Training Priority: {{weight}}", "support.wallet.subscription.Update extra ai points": "Extra AI Points", "support.wallet.subscription.Update extra dataset size": "Storage", "support.wallet.subscription.Upgrade plan": "Upgrade Package", "support.wallet.subscription.ai_model": "AI Language Model", "support.wallet.subscription.function.History store": "{{amount}} Days of Chat History Retention", "support.wallet.subscription.function.Max app": "{{amount}} App limit", "support.wallet.subscription.function.Max dataset": "{{amount}} Dataset limit", "support.wallet.subscription.function.Max dataset size": "{{amount}} Dataset Indexes", "support.wallet.subscription.function.Max members": "{{amount}} Employee limit", "support.wallet.subscription.function.Points": "{{amount}} AI Points", "support.wallet.subscription.mode.Month": "Month", "support.wallet.subscription.mode.Period": "Subscription Period", "support.wallet.subscription.mode.Year": "Year", "support.wallet.subscription.mode.Year sale": "Two Months Free", "support.wallet.subscription.point": "Points", "support.wallet.subscription.standardSubLevel.custom": "Custom", "support.wallet.subscription.standardSubLevel.enterprise": "Enterprise", "support.wallet.subscription.standardSubLevel.enterprise_desc": "Suitable for small and medium-sized enterprises to build Dataset applications in production environments", "support.wallet.subscription.standardSubLevel.experience": "Experience", "support.wallet.subscription.standardSubLevel.experience_desc": "Unlock the full functionality of FastGPT", "support.wallet.subscription.standardSubLevel.free": "Free", "support.wallet.subscription.standardSubLevel.free desc": "Free trial of core features. \nIf you haven't logged in for 30 days, the knowledge base will be cleared.", "support.wallet.subscription.standardSubLevel.team": "Team", "support.wallet.subscription.standardSubLevel.team_desc": "Suitable for small teams to build Dataset applications and provide external services", "support.wallet.subscription.status.active": "Active", "support.wallet.subscription.status.expired": "Expired", "support.wallet.subscription.status.inactive": "Inactive", "support.wallet.subscription.team_operation_log": "Record team operation logs", "support.wallet.subscription.token_compute": "Click to View Online Tokens Calculator", "support.wallet.subscription.type.balance": "Balance Recharge", "support.wallet.subscription.type.extraDatasetSize": "Dataset Expansion", "support.wallet.subscription.type.extraPoints": "AI Points Package", "support.wallet.subscription.type.standard": "Package Subscription", "support.wallet.subscription.web_site_sync": "Website Sync", "support.wallet.usage.Ai model": "AI Model", "support.wallet.usage.App name": "App Name", "support.wallet.usage.Audio Speech": "Voice Playback", "support.wallet.usage.Bill Module": "Billing <PERSON>", "support.wallet.usage.Duration": "Duration (seconds)", "support.wallet.usage.Module name": "Module Name", "support.wallet.usage.Source": "Source", "support.wallet.usage.Text Length": "Text Length", "support.wallet.usage.Time": "Generation Time", "support.wallet.usage.Token Length": "Token Length", "support.wallet.usage.Total": "Total Amount", "support.wallet.usage.Total points": "AI Points Consumption", "support.wallet.usage.Usage Detail": "Usage Details", "support.wallet.usage.Whisper": "Voice Input", "sync_link": "Sync Link", "sync_success": "Synced Successfully", "system.Concat us": "Contact Us", "system.Help Document": "Help Document", "system_help_chatbot": "Help Chatbot", "tag_list": "Tag List", "team_tag": "Team Tag", "templateTags.Image_generation": "Image generation", "templateTags.Office_services": "Office Services", "templateTags.Roleplay": "role play", "templateTags.Web_search": "Search online", "templateTags.Writing": "Writing", "template_market": "Template Market", "textarea_variable_picker_tip": "Enter \"/\" to select a variable", "to_dataset": "To dataset", "ui.textarea.Magnifying": "Magnifying", "un_used": "Unused", "unauth_token": "The certificate has expired, please log in again", "undo_tip": "Undo  ctrl  z", "undo_tip_mac": "Undo  ⌘  z ", "unit.character": "Character", "unit.minute": "Minute", "unit.seconds": "Second", "unknow_source": "Unknown Source", "unusable_variable": "No Usable Variables", "update_failed": "Update Failed", "update_success": "Updated Successfully", "upload_file": "Upload File", "upload_file_error": "File Upload Failed", "use_helper": "Use Helper", "user.Account": "Account", "user.Amount of earnings": "Earnings (￥)", "user.Amount of inviter": "Total Number of Invites", "user.Application Name": "Project Name", "user.Avatar": "Avatar", "user.Change": "Change", "user.Copy invite url": "Copy Invite Link", "user.Edit name": "Click to <PERSON>", "user.Invite Url": "Invite Link", "user.Invite url tip": "Friends registered through this link will be permanently bound to you, and you will receive a balance reward when they recharge.\nAdditionally, you will immediately receive a 5 yuan reward when friends register with their phone number.\nThe reward will be sent to your default team.", "user.Laf Account Setting": "Laf Account Configuration", "user.Language": "Language", "user.Employee Name": "Nickname", "user.No_right_to_reset_password": "You do not have the right to reset the password", "user.Notification Receive": "Notification Receive", "user.Notification Receive Bind": "Please bind the notification receive method first", "user.Old password is error": "Old Password is Incorrect", "user.OpenAI Account Setting": "OpenAI Account Configuration", "user.Password": "Password", "user.Password has no change": "New password is the same as the old password", "user.Pay": "Recharge", "user.Promotion": "Promotion", "user.Promotion Rate": "Cashback Rate", "user.Promotion rate tip": "You will receive a balance reward when friends recharge", "user.Replace": "Replace", "user.Set OpenAI Account Failed": "Failed to Set OpenAI Account", "user.Team": "Team", "user.Time": "Time", "user.Timezone": "Timezone", "user.Update Password": "Update Password", "user.Update password failed": "Failed to Update Password", "user.Update password successful": "Password Updated Successfully", "user.apikey.key": "API Key", "user.confirm_password": "Confirm Password", "user.init_password": "Please initialize password", "user.new_password": "New Password", "user.no_invite_records": "No Invite Records", "user.no_notice": "No Notices", "user.no_usage_records": "No Usage Records", "user.old_password": "Old Password", "user.password_message": "Password must be at least 4 characters and at most 60 characters", "user.password_tip": "Password must be at least 8 characters long and contain at least two combinations: numbers, letters, or special characters", "user.reset_password": "Reset Password", "user.reset_password_tip": "The initial password is not set/the password has not been modified for a long time, please reset the password", "user.team.Balance": "Team Balance", "user.team.Check Team": "Switch", "user.team.Leave Team": "Leave Team", "user.team.Leave Team Failed": "Failed to Leave Team", "user.team.Employee": "Employee", "user.team.Employee Name": "Employee Name", "user.team.Over Max Employee Tip": "The team can have up to {{max}} people", "user.team.Personal Team": "Personal Team", "user.team.Processing invitations": "Processing Invitations", "user.team.Processing invitations Tips": "You have {{amount}} team invitations to process", "user.team.Remove Employee Confirm Tip": "Confirm to remove {{username}} from the team?", "user.team.Select Team": "Select Team", "user.team.Switch Team Failed": "Failed to Switch Team", "user.team.Tags Async": "Save", "user.team.Team Tags Async": "Tag Sync", "user.team.Team Tags Async Success": "<PERSON> Error Successful, Tag Information Updated", "user.team.invite.Accepted": "Joined Team", "user.team.invite.Deal Width Footer Tip": "It will automatically close after processing", "user.team.invite.Reject": "Invitation Rejected", "user.team.member.Confirm Leave": "Confirm to leave this team?", "user.team.member.active": "Joined", "user.team.member.reject": "Rejected", "user.team.member.waiting": "Pending Acceptance", "user.team.role.Admin": "Admin", "user.team.role.Owner": "Owner", "user.team.role.Visitor": "visitor", "user.team.role.writer": "writable employee", "user.type": "Type", "user_leaved": "Leaved", "value": "Value", "verification": "Verification", "workflow.template.communication": "Communication", "xx_search_result": "{{key}} Search Results", "yes": "Yes", "yesterday": "yesterday", "yesterday_detail_time": "Yesterday {{time}}", "zoomin_tip": "Zoom Out  ctrl  -", "zoomin_tip_mac": "Zoom Out  ⌘  -", "zoomout_tip": "Zoom In  ctrl  +", "zoomout_tip_mac": "Zoom In  ⌘  +"}