{"bill.balance": "Balance", "bill.buy_plan": "Purchase Plan", "bill.contact_customer_service": "Contact Support", "bill.conversion": "Conversion", "bill.convert_error": "Conversion Failed", "bill.convert_success": "Conversion Successful", "bill.current_token_price": "Current Token <PERSON>", "bill.not_need_invoice": "Balance payment, invoice not available", "bill.price": "Price", "bill.renew_plan": "Renew Plan", "bill.standard_valid_tip": "Plan Usage Rules: Higher-level plans will be used first. Unused plans will be activated later.", "bill.token_expire_1year": "Tokens are valid for one year", "bill.tokens": "Tokens", "bill.use_balance": "Use Balance", "bill.use_balance_hint": "Due to system upgrade, the 'Auto-renewal from balance' mode is canceled, and the balance recharge option is closed. Your balance can be used to purchase tokens.", "bill.valid_time": "Effective Time", "bill.you_can_convert": "You can convert", "bill.yuan": "Yuan", "delete.admin_failed": "Failed to Delete Admin", "delete.admin_success": "Admin Deleted Successfully", "delete.failed": "Delete failed", "delete.success": "Delete successfully", "has_chosen": "Selected", "login.Dingtalk": "DingT<PERSON>gin", "login.error": "<PERSON><PERSON>", "login.password_condition": "Password can be up to 60 characters", "login.success": "Login Successful", "manage_team": "Manage team", "name": "Name", "new_password": "New Password", "notification.remind_owner_bind": "Please remind the creator to bind a notification account", "operations": "Actions", "owner": "owner", "password.code_required": "Verification Code Required", "password.code_send_error": "Failed to Send Verification Code", "password.code_sended": "Verification Code Sent", "password.confirm": "Confirm Password", "password.email_phone_error": "Invalid Email/Phone Number Format", "password.email_phone_void": "Email/Phone Number Cannot Be Empty", "password.not_match": "Passwords Do Not Match", "password.password_condition": "Password must be between 4 and 20 characters", "password.password_required": "Password Cannot Be Empty", "password.retrieve": "Retrieve Password", "password.retrieved": "Password Retrieved", "password.retrieved_account": "Retrieve {{account}} Account", "password.to_login": "Go to Login", "password.verification_code": "Verification Code", "permission.Add": "Add Permissions", "permission.Manage": "Admin", "permission.Manage tip": "Team admin with full permissions", "permission.Read": "Read Only", "permission.Read desc": "Employees can only read related resources, cannot create new resources", "permission.Write": "Write", "permission.Write tip": "In addition to read access, can create new resources", "permission.only_collaborators": "Collaborators Only", "permission.team_read": "Team Read Access", "permission.team_write": "Team Write Access", "permission_add_tip": "After adding, you can check the permissions for them.", "permission_des.manage": "Can create resources, invite, and delete members", "permission_des.read": "Employees can only read related resources and cannot create new resources.", "permission_des.write": "In addition to readable resources, you can also create new resources", "permissions": "Permissions", "personal_information": "Me", "personalization": "Personalization", "promotion_records": "Promotion", "register.confirm": "Confirm Registration", "register.register_account": "Register {{account}} Account", "register.success": "Registration Successful", "register.to_login": "Already have an account? Go to Login", "search_group_org_user": "Search employee/role/org name", "search_user": "Search Username", "sso_auth_failed": "SSO authentication failed", "synchronization.button": "Sync Now", "synchronization.placeholder": "Enter Sync Tag", "synchronization.title": "Enter the sync tag link and click the sync button to synchronize", "team.Add manager": "Add Admin", "team.Confirm Invite": "Confirm invitation", "team.Create Team": "Create new team", "team.Invite Member Failed Tip": "An exception occurred when inviting members", "team.Invite Member Result Tip": "Invitation result prompt", "team.Invite Member Success Tip": "Invite members to complete\n\nSuccess: {{success}} people\n\nInvalid username: {{inValid}}\n\nAlready in team: {{inTeam}}", "team.Set Name": "Give the team a name", "team.Team Name": "Team name", "team.Update Team": "Update team information", "team.add_collaborator": "Add Collaborator", "team.add_permission": "Add permissions", "team.add_writer": "Add writable members", "team.avatar_and_name": "avatar", "team.belong_to_group": "Employee role", "team.group.avatar": "Group avatar", "team.group.create": "Create role", "team.group.create_failed": "Failed to create role", "team.group.default_group": "Default role", "team.group.delete_confirm": "Confirm to delete role?", "team.group.edit": "Edit role", "team.group.edit_info": "Edit information", "team.group.group": "role", "team.group.keep_admin": "Keep administrator rights", "team.group.manage_member": "Managing members", "team.group.manage_tip": "Can manage members, create groups, manage all groups, assign permissions to groups and members", "team.group.members": "employee", "team.group.name": "Group name", "team.group.permission.write": "Workbench/knowledge base creation", "team.group.permission_tip": "Employees with individually configured permissions will follow the individual permission configuration and will no longer be affected by role permissions.\n\nIf a employee is in multiple permission groups, the employee's permissions are combined.", "team.group.role.admin": "administrator", "team.group.role.member": "employee", "team.group.role.owner": "owner", "team.group.set_as_admin": "Set as administrator", "team.group.toast.can_not_delete_owner": "Owner cannot be deleted, please transfer first", "team.group.transfer_owner": "transfer owner", "team.manage_collaborators": "Manage Collaborators", "team.no_collaborators": "No Collaborators", "team.org.org": "Organization", "team.write_role_member": "Write Permission", "team.collaborator.added": "Added"}