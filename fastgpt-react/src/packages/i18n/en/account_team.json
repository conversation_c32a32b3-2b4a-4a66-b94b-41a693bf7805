{"1person": "1 person", "1year": "1 Year", "30mins": "30 Minutes", "7days": "7 Days", "accept": "accept", "action": "operate", "admin_add_plan": "Add a team package", "admin_add_user": "Add a user", "admin_change_license": "Change of license", "admin_create_app_template": "Add a template", "admin_create_plugin": "Add plugins", "admin_create_plugin_group": "Create plugin grouping", "admin_delete_app_template": "Delete the template", "admin_delete_plugin": "Plugin Delete", "admin_delete_plugin_group": "Delete plugin grouping", "admin_delete_template_type": "Delete template classification", "admin_finish_invoice": "Issuing an invoice", "admin_login": "Administrator login", "admin_save_template_type": "Update template classification", "admin_send_system_inform": "Send system notifications", "admin_update_app_template": "Update templates", "admin_update_plan": "Editorial Team Package", "admin_update_plugin": "Plugin Update", "admin_update_plugin_group": "Plugin group update", "admin_update_system_config": "System configuration update", "admin_update_system_modal": "System announcement configuration", "admin_update_team": "Edit team information", "admin_update_user": "Edit User", "assign_permission": "Permission change", "audit_log": "audit", "change_department_name": "Organizational Editor", "change_member_name": "Member name change", "change_member_name_self": "Change member name", "change_notification_settings": "Change the way to receive notifications", "change_password": "change password", "confirm_delete_from_org": "Confirm to move {{username}} out of the organizational?", "confirm_delete_from_team": "Confirm to move {{username}} out of the team?", "confirm_delete_group": "Confirm to delete group?", "confirm_delete_org": "Confirm to delete organization?", "confirm_forbidden": "Confirm forbidden", "confirm_leave_team": "Confirmed to leave the team?  \nAfter exiting, all your resources in the team are transferred to the team owner.", "copy_link": "Copy link", "create_api_key": "Create API key", "create_app": "Create an application", "create_app_copy": "Create a copy of the application", "create_app_folder": "Create an application folder", "create_app_publish_channel": "Create a sharing channel", "create_data": "Insert data", "create_dataset": "Create a knowledge base", "create_dataset_folder": "Create a Knowledge Base Folder", "create_department": "Create a sub-organizational", "create_group": "Create group", "create_invitation_link": "Create Invitation Link", "create_invoice": "Issuing invoices", "create_org": "Create organization", "create_sub_org": "Create sub-organization", "dataset.api_file": "API Import", "dataset.common_dataset": "Dataset", "dataset.external_file": "External File", "dataset.feishu_dataset": "<PERSON><PERSON><PERSON>she<PERSON>", "dataset.folder_dataset": "Folder", "dataset.website_dataset": "Website Sync", "dataset.yuque_dataset": "Yuque Knowledge Base", "delete": "delete", "delete_api_key": "Delete the API key", "delete_app": "Delete the workbench application", "delete_app_collaborator": "App permissions delete", "delete_app_publish_channel": "Delete the publishing channel", "delete_collection": "Delete a collection", "delete_data": "Delete data", "delete_dataset": "Delete the knowledge base", "delete_dataset_collaborator": "Knowledge Base Permission Deletion", "delete_department": "Delete sub-organizational", "delete_from_org": "Move out of organizational", "delete_from_team": "Move out of the team", "delete_group": "Delete a group", "delete_org": "Delete organization", "department": "organizational", "edit_info": "Edit information", "edit_member": "Edit user", "edit_member_tip": "Name", "edit_org_info": "Edit organization information", "expires": "Expiration time", "export_app_chat_log": "Export the app chat history", "export_bill_records": "Export billing history", "export_dataset": "Export knowledge base", "export_members": "Export members", "forbid_hint": "After forbidden, this invitation link will become invalid. This action is irreversible. Are you sure you want to deactivate?", "forbid_success": "Forbid success", "forbidden": "Forbidden", "group": "group", "group_name": "Group name", "handle_invitation": "Handle Invitation", "has_forbidden": "Forbidden", "has_invited": "Invited", "ignore": "Ignore", "inform_level_common": "Normal", "inform_level_emergency": "Emergency", "inform_level_important": "Important", "invitation_copy_link": "[{{systemName}}] {{userName}} invites you to join the {{teamName}} team, link: {{url}}", "invitation_link_auto_clean_hint": "Expired links will be automatically cleaned up after 30 days", "invitation_link_description": "Link description", "invitation_link_list": "Invitation link list", "invite_member": "Invite members", "invited": "Invited", "join_team": "Join the team", "join_update_time": "Join/Update Time", "kick_out_team": "Remove members", "label_sync": "Tag sync", "leave": "Resigned", "leave_team_failed": "Leaving the team exception", "log_admin_add_plan": "【{{name}}】A package will be added to a team with a team id [{{teamId}}]", "log_admin_add_user": "【{{name}}】Create a user named [{{userName}}]", "log_admin_change_license": "【{{name}}】Changed License", "log_admin_create_app_template": "【{{name}}】Added a template named [{{templateName}}]", "log_admin_create_plugin": "【{{name}}】Added plugin named [{{pluginName}}]", "log_admin_create_plugin_group": "【{{name}}】Create a plug-in group called [{{groupName}}]", "log_admin_delete_app_template": "【{{name}}】Deleted the template named [{{templateName}}]", "log_admin_delete_plugin": "【{{name}}】Remove plugin named [{{pluginName}}]", "log_admin_delete_plugin_group": "【{{name}}】Deleted plug-in grouping named [{{groupName}}]", "log_admin_delete_template_type": "【{{name}}】Deleted the template classification named [{{typeName}}]", "log_admin_finish_invoice": "【{{name}}】Issued an invoice to a team named [{{teamName}}]", "log_admin_login": "【{{name}}】Logined in the administrator background", "log_admin_save_template_type": "【{{name}}】Added template classification called [{{typeName}}]", "log_admin_send_system_inform": "【{{name}}】Sent a system notification titled [{{informTitle}}], with the level of [{{level}}]", "log_admin_update_app_template": "【{{name}}】Updated template information named [{{templateName}}]", "log_admin_update_plan": "【{{name}}】Edited the package information of the team with the team id [{{teamId}}]", "log_admin_update_plugin": "【{{name}}】Updated plugin information called [{{pluginName}}]", "log_admin_update_plugin_group": "【{{name}}】Updated plug-in grouping called [{{groupName}}]", "log_admin_update_system_config": "【{{name}}】Updated system configuration", "log_admin_update_system_modal": "【{{name}}】The system announcement configuration was carried out", "log_admin_update_team": "[{{name}}] Replace the team editing information named [{{teamName}}] to the team name: [{{newTeamName}}], balance: [{{newBalance}}]", "log_admin_update_user": "Modify the user information of 【{{userName}}】", "log_assign_permission": "[{{name}}] Updated the permissions of [{{objectName}}]: [Application creation: [{{appCreate}}], Knowledge Base: [{{datasetCreate}}], API Key: [{{apiKeyCreate}}], Management: [{{manage}}]]", "log_change_department": "【{{name}}】Updated department【{{departmentName}}】", "log_change_member_name": "【{{name}}】Rename member [{{memberName}}] to 【{{newName}}】", "log_change_member_name_self": "【{{name}}】Change your member name to 【{{newName}}】", "log_change_notification_settings": "【{{name}}】A change notification receiving method operation was carried out", "log_change_password": "【{{name}}】The password change operation was performed", "log_create_api_key": "【{{name}}】Create an API key named [{{keyName}}]", "log_create_app": "【{{name}}】Created [{{appType}}] named [{{appName}}]", "log_create_app_copy": "【{{name}}] Created a copy of [{{appType}}] named [{{appName}}]", "log_create_app_folder": "【{{name}}】Create a folder named [{{folderName}}]", "log_create_app_publish_channel": "[{{name}}] Created a channel named [{{channelName}}] for [{{appType}}] called [{{appName}}].", "log_create_collection": "[{{name}}] Create a collection named [{{collectionName}}] in [{{datasetType}}] called [{{datasetName}}].", "log_create_data": "[{{name}}] Insert data into a collection named [{{datasetName}}] in [{{datasetType}}] called [{{datasetName}}] into a collection named [{{collectionName}}]", "log_create_dataset": "【{{name}}】Created 【{{datasetType}}】 named 【{{datasetName}}】", "log_create_dataset_folder": "【{{name}}】Created a folder named {{folderName}}】", "log_create_department": "【{{name}}】Department【{{departmentName}}】", "log_create_group": "【{{name}}】Created group [{{groupName}}]", "log_create_invitation_link": "【{{name}}】Created invitation link【{{link}}】", "log_create_invoice": "【{{name}}】Invoice operation was carried out", "log_delete_api_key": "【{{name}}】Deleted the API key named [{{keyName}}]", "log_delete_app": "【{{name}}】Delete the [{{appType}}] named [{{appName}}]", "log_delete_app_collaborator": "【{{name}}】Delete the [itemName] permission named [itemValueName] in [{{appType}}] named [{{appName}}] delete the [itemName] permission named [{{appName}}] named [{{appName}}] named [{{appName}}] deleted the [{{itemName}}] permission named [{{itemValueName}}] named [{{appType}}] named [{{appName}}].", "log_delete_app_publish_channel": "[{{name}}] [{{appType}}] named [{{appName}}] deleted the channel named [{{channelName}}]", "log_delete_collection": "[{{name}}] Deleted a collection named [{{collectionName}}] in [{{datasetType}}] named [{{datasetName}}].", "log_delete_data": "[{{name}}] Delete data in a collection named [{{datasetName}}] in a collection named [{{datasetName}}]", "log_delete_dataset": "【{{name}}】Deleted 【{{datasetType}}】 named [{{datasetName}}]", "log_delete_dataset_collaborator": "【{{name}}】Updated the collaborators of 【{{appType}}】 named 【{{appName}}】 to: Organization: 【{{orgList}}】, Group: 【{{groupList}}】, Member 【{{tmbList}}】; updated the permissions to: Read permission: 【{{readPermission}}】, Write permission: 【{{writePermission}}】, Administrator permission: 【{{managePermission}}】", "log_delete_department": "{{name}} deleted organizational {{departmentName}}", "log_delete_group": "{{name}} deleted group {{groupName}}", "log_details": "Details", "log_export_app_chat_log": "【{{name}}】Export a chat history called [{{appName}}] called [{{appType}}]", "log_export_bill_records": "【{{name}}】Export the billing record", "log_export_dataset": "[{{name}}] Export [{{datasetType}}] called [{{datasetName}}]", "log_join_team": "【{{name}}】Join the team through the invitation link 【{{link}}】", "log_kick_out_team": "{{name}} removed member {{memberName}}", "log_login": "【{{name}}】Logined in the system", "log_move_app": "【{{name}}】Move [{{appType}}] named [{{appName}}] to [{{targetFolderName}}]", "log_move_dataset": "【{{name}}】Move [{{datasetType}}] named [{{datasetName}}] to [{{targetFolderName}}]", "log_recover_team_member": "【{{name}}】Restored member【{{memberName}}】", "log_relocate_department": "【{{name}}】Displayed department【{{departmentName}}】", "log_retrain_collection": "[{{name}}] Retrained the collection named [{{collectionName}}] in [{{datasetType}}] called [{{datasetName}}].", "log_search_test": "【{{name}}】Perform a search test operation on [{{datasetType}}] named [{{datasetName}}]", "log_set_invoice_header": "【{{name}}】The invoice header operation was set up", "log_time": "Operation time", "log_transfer_app_ownership": "【{{name}}] Transfer ownership of [{{appType}}] named [{{appName}}] from [{oldOwnerName}}] to [{{newOwnerName}}]", "log_transfer_dataset_ownership": "[{{name}}] Transfer ownership of [{{datasetType}}] named [{{datasetName}}] from [{oldOwnerName}}] to [{{newOwnerName}}]", "log_type": "Operation Type", "log_update_api_key": "【{{name}}】Updated the API key named [{{keyName}}]", "log_update_app_collaborator": "[{{name}}] Updated the collaborator named [{{appName}}] to: Organization: [{{orgList}}], Group: [{{groupList}}], Member [{{tmbList}}]; permissions updated to: Read permission: [{{readPermission}}], Write permission: [{{writePermission}}], Administrator permission: [{{managePermission}}]", "log_update_app_info": "[{{name}}] updated [{{appType}}] named [{{appName}}]: [{{newItemNames}}] to [{{newItemValues}}]", "log_update_app_publish_channel": "[{{name}}] Updated a channel named [{{channelName}}] for [{{appType}}] called [{{appName}}].", "log_update_collection": "[{{name}}] Updated a collection named [{{collectionName}}] in [{{datasetType}}] called [{{datasetName}}].", "log_update_data": "【{{name}}】Update data in a collection named 【{{datasetName}}】[{{datasetType}}] with [{{datasetType}}] with [{{collectionName}}]", "log_update_dataset": "【{{name}}】Updated [{{datasetType}}] named [{{datasetName}}]", "log_update_dataset_collaborator": "[{{name}}] Updated the collaborator named [{{datasetName}}] to: Organization: [{{orgList}}], Group: [{{groupList}}], Member [{{tmbList}}]; permissions updated to: [{{readPermission}}], [{{writePermission}}], [{{managePermission}}]", "log_update_publish_app": "【{{name}}】【{{operationName}}】【{{appType}}】 named [{{appName}}】", "log_user": "Operator", "login": "Log in", "manage_member": "Managing members", "member": "member", "member_group": "Belonging to member group", "move_app": "App location movement", "move_dataset": "Mobile Knowledge Base", "move_member": "Move member", "move_org": "Move organization", "notification_recieve": "Team notification reception", "org": "organization", "org_description": "Organization description", "org_name": "Organization name", "owner": "owner", "permission": "Permissions", "permission_apikeyCreate": "Create API Key", "permission_apikeyCreate_Tip": "You can create global APIKey and MCP services", "permission_appCreate": "Create Application", "permission_appCreate_tip": "Can create applications in the root directory (creation permissions in folders are controlled by the folder)", "permission_datasetCreate": "Create Knowledge Base", "permission_datasetCreate_Tip": "Can create knowledge bases in the root directory (creation permissions in folders are controlled by the folder)", "permission_manage": "Admin", "permission_manage_tip": "Can manage members, create groups, manage all groups, and assign permissions to groups and members", "please_bind_contact": "Please bind the contact information", "purchase_plan": "Upgrade package", "recover_team_member": "Member Recovery", "relocate_department": "Organizational Mobile", "remark": "remark", "remove_tip": "Confirm to remove {{username}} from the team?", "restore_tip": "Confirm to join the team {{username}}? \nOnly the availability and related permissions of this member account are restored, and the resources under the account cannot be restored.", "restore_tip_title": "Recovery confirmation", "retain_admin_permissions": "Keep administrator rights", "retrain_collection": "Retrain the set", "save_and_publish": "save and publish", "search_log": "Search log", "search_member": "Search for members", "search_member_group_name": "Search member/group name", "search_org": "Search Organizational", "search_test": "Search Test", "set_invoice_header": "Set up invoice header", "set_name_avatar": "Team avatar", "sync_immediately": "Synchronize now", "sync_member_failed": "Synchronization of members failed", "sync_member_success": "Synchronize members successfully", "total_team_members": "Total {{amount}} members", "transfer_app_ownership": "Transfer app ownership", "transfer_dataset_ownership": "Transfer dataset ownership", "transfer_ownership": "Transfer ownership", "type.Folder": "Folder", "type.Http plugin": "HTTP Plugin", "type.Plugin": "Plugin", "type.Simple bot": "Simple App", "type.Tool": "Tool", "type.Tool set": "Toolset", "type.Workflow bot": "Workflow", "unlimited": "Unlimited", "update": "update", "update_api_key": "Update API key", "update_app_collaborator": "Apply permission changes", "update_app_info": "Application information modification", "update_app_publish_channel": "Update the release channel", "update_collection": "Update the collection", "update_data": "Update data", "update_dataset": "Update the knowledge base", "update_dataset_collaborator": "Knowledge Base Permission Changes", "update_publish_app": "Application update", "used_times_limit": "Limit", "user_name": "username", "user_team_invite_member": "Invite members", "user_team_leave_team": "Leave the team", "user_team_leave_team_failed": "Failure to leave the team", "waiting": "To be accepted"}