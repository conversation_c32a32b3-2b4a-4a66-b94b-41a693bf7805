{"Action": "操作", "Add": "新增", "Add_new_input": "新增輸入", "All": "全部", "App": "應用程式", "Cancel": "取消", "Choose": "選擇", "Click_to_expand": "點選檢視詳細資訊", "Close": "關閉", "Code": "原始碼", "Config": "設定", "Confirm": "確認", "Continue_Adding": "繼續新增", "Copy": "複製", "Creating": "建立中", "Delete": "刪除", "Detail": "詳細資料", "Documents": "文件", "Done": "完成", "Download": "下載", "Edit": "編輯", "Error": "錯誤", "Exit": "離開", "Export": "匯出", "FAQ.ai_point_a": "每次調用AI模型時，都會消耗一定的AI積分。\n具體的計算標準可參考上方的“AI 積分計算標準”。\n系統會優先採用模型廠商返回的實際 usage，若為空，則採用GPT3.5的計算方式進行估算，1Token≈0.7中文字符≈0.9英文單詞，連續出現的字符可能被認為是1個Tokens。", "FAQ.ai_point_expire_a": "會過期。目前方案過期後，AI 點數將會清空並更新為新方案的 AI 點數。年度方案的 AI 點數有效期為一年，而不是每個月重設。", "FAQ.ai_point_expire_q": "AI 點數會過期嗎？", "FAQ.ai_point_q": "什麼是 AI 點數？", "FAQ.check_subscription_a": "帳號 - 個人資訊 - 方案詳細資訊 - 使用狀況。您可以檢視已訂閱方案的生效和到期時間。當付費方案到期後，將自動切換為免費版。", "FAQ.check_subscription_q": "在哪裡可以檢視已訂閱的方案？", "FAQ.dataset_compute_a": "1條知識庫存儲等於1條知識庫索引。\n一條分塊數據，通常對應多條索引，可以在單個知識庫集合中看到\"n組索引\"", "FAQ.dataset_compute_q": "知識庫儲存如何計算？", "FAQ.dataset_index_a": "不會，但知識庫索引超出限制時，將無法插入和更新知識庫內容。", "FAQ.dataset_index_q": "知識庫索引超出是否會被刪除？", "FAQ.free_user_clean_a": "若免費版團隊（免費版且未購買額外方案）連續 30 天未登入系統，系統會自動清除該團隊下所有知識庫內容。", "FAQ.free_user_clean_q": "免費版的資料會被清除嗎？", "FAQ.package_overlay_a": "可以。每次購買的資源包都是獨立的，在其有效期內會重疊使用。AI 點數會優先從最早到期的資源包中扣除。", "FAQ.package_overlay_q": "額外資源包可以重疊使用嗎？", "FAQ.switch_package_a": "方案使用規則為優先使用較進階的方案。因此，若購買的新方案比目前方案更進階，則新方案會立即生效；否則將繼續使用目前方案。", "FAQ.switch_package_q": "是否會切換訂閱方案？", "File": "檔案", "Finish": "完成", "Folder": "資料夾", "FullScreen": "全屏", "FullScreenLight": "全屏預覽", "Import": "匯入", "Input": "輸入", "Instructions": "使用說明", "Intro": "介紹", "Loading": "載入中...", "Login": "登入", "More": "更多", "Move": "移動", "Name": "名稱", "None": "無", "OK": "確定", "Open": "開啟", "Operation": "操作", "Other": "其他", "Output": "輸出", "Params": "參數", "Parse": "解析", "Permission": "權限", "Permission_tip": "個人權限大於角色權限", "Preview": "預覽", "Remove": "移除", "Rename": "重新命名", "Required_input": "必填", "Reset": "恢復預設", "Restart": "重新開始", "Resume": "繼續", "Role": "權限", "Run": "執行", "Running": "執行中", "Save": "儲存", "Save_and_exit": "儲存並離開", "Search": "搜尋", "Select_all": "全選", "Setting": "設定", "Status": "狀態", "Submit": "送出", "Success": "成功", "Team": "團隊", "UnKnow": "未知", "Unlimited": "無限制", "Update": "更新", "Username": "使用者名稱", "Waiting": "等待中", "Warning": "警告", "Website": "網站", "action_confirm": "確認", "add_new": "新增", "add_new_param": "新增參數", "add_success": "新增成功", "all_quotes": "全部引用", "all_result": "完整結果", "app_not_version": "該應用未發布過，請先發布應用", "auth_config": "鑑權配置", "auth_type": "鑑權類型", "auth_type.Custom": "自定義", "auth_type.None": "無", "back": "返回", "base_config": "基本設定", "bill_already_processed": "訂單已處理", "bill_expired": "訂單已過期", "bill_not_pay_processed": "非在線訂單", "button.extra_dataset_size_tip": "您正在購買【額外知識庫容量】", "button.extra_points_tip": "您正在購買【額外 AI 積分】", "can_copy_content_tip": "無法使用瀏覽器自動複製，請手動複製下面內容", "chart_mode_cumulative": "累積", "chart_mode_incremental": "分時", "choosable": "可選擇", "chose_condition": "選擇條件", "chosen": "已選擇", "classification": "分類", "click_drag_tip": "點選可拖曳", "click_select_avatar": "點選選擇頭像", "click_to_copy": "點選複製", "click_to_resume": "點選繼續", "code_editor": "程式碼編輯器", "code_error.account_error": "帳號名稱或密碼錯誤", "code_error.account_exist": "賬號已註冊", "code_error.account_not_found": "使用者未註冊", "code_error.app_error.invalid_app_type": "無效的應用程式類型", "code_error.app_error.invalid_owner": "非法的應用程式擁有者", "code_error.app_error.not_exist": "應用程式不存在", "code_error.app_error.un_auth_app": "無權操作此應用程式", "code_error.chat_error.un_auth": "沒有權限操作此對話記錄", "code_error.error_code.400": "請求失敗", "code_error.error_code.401": "無存取權限", "code_error.error_code.403": "禁止存取", "code_error.error_code.404": "請求不存在", "code_error.error_code.405": "請求方法錯誤", "code_error.error_code.406": "請求格式錯誤", "code_error.error_code.410": "資源已刪除", "code_error.error_code.422": "驗證錯誤", "code_error.error_code.500": "伺服器錯誤", "code_error.error_code.502": "閘道錯誤", "code_error.error_code.503": "伺服器過載或維護中", "code_error.error_code.504": "閘道逾時", "code_error.error_code[429]": "請求過於頻繁", "code_error.error_message.403": "憑證錯誤", "code_error.error_message.510": "帳戶餘額不足", "code_error.error_message.511": "無權操作此模型", "code_error.error_message.513": "無權讀取此檔案", "code_error.error_message.514": "API 金鑰無效", "code_error.openapi_error.api_key_not_exist": "API 金鑰不存在", "code_error.openapi_error.exceed_limit": "最多 10 組 API 金鑰", "code_error.openapi_error.un_auth": "無權操作此 API 金鑰", "code_error.outlink_error.invalid_link": "分享連結無效", "code_error.outlink_error.link_not_exist": "分享連結不存在", "code_error.outlink_error.un_auth_user": "身份驗證失敗", "code_error.plugin_error.not_exist": "工具不存在", "code_error.plugin_error.un_auth": "無權操作該工具", "code_error.system_error.community_version_num_limit": "超出開源版數量限制，請升級商業版：https://tryfastgpt.ai", "code_error.system_error.license_app_amount_limit": "超出系統最大應用數量", "code_error.system_error.license_dataset_amount_limit": "超出系統最大知識庫數量", "code_error.system_error.license_user_amount_limit": "超出系統最大用戶數量", "code_error.team_error.ai_points_not_enough": "AI 點數不足", "code_error.team_error.app_amount_not_enough": "已達應用程式數量上限", "code_error.team_error.cannot_delete_default_group": "無法刪除預設角色", "code_error.team_error.cannot_delete_non_empty_org": "無法刪除非空組織", "code_error.team_error.cannot_modify_root_org": "無法修改根組織", "code_error.team_error.cannot_move_to_sub_path": "無法移動到相同或子目錄", "code_error.team_error.dataset_amount_not_enough": "已達知識庫數量上限", "code_error.team_error.dataset_size_not_enough": "知識庫容量不足，請先擴充容量", "code_error.team_error.group_name_duplicate": "角色名稱重複", "code_error.team_error.group_name_empty": "角色名稱不能為空", "code_error.team_error.group_not_exist": "角色不存在", "code_error.team_error.invitation_link_invalid": "邀請連結已失效", "code_error.team_error.not_user": "找不到該用戶", "code_error.team_error.org_member_duplicated": "重複的組織用戶", "code_error.team_error.org_member_not_exist": "組織用戶不存在", "code_error.team_error.org_not_exist": "組織不存在", "code_error.team_error.org_parent_not_exist": "父組織不存在", "code_error.team_error.over_size": "團隊用戶超出限制", "code_error.team_error.plugin_amount_not_enough": "已達外掛程式數量上限", "code_error.team_error.re_rank_not_enough": "免費版無法使用檢索重排~", "code_error.team_error.too_many_invitations": "您的有效邀請連結數已達上限，請先清理連結", "code_error.team_error.un_auth": "無權操作此團隊", "code_error.team_error.user_not_active": "使用者未接受或已離開團隊", "code_error.team_error.website_sync_not_enough": "免費版無法使用 Web 站點同步~", "code_error.team_error.you_have_been_in_the_team": "你已經在該團隊中", "code_error.token_error_code.403": "登入狀態無效，請重新登入", "code_error.user_error.balance_not_enough": "帳戶餘額不足", "code_error.user_error.bin_visitor_guest": "您目前身份為訪客，無權操作", "code_error.user_error.un_auth_user": "找不到此使用者", "comfirm_import": "確認匯入", "comfirm_leave_page": "確認離開此頁面？", "comfirn_create": "確認建立", "commercial_function_tip": "請升級為商業版後使用此功能：https://doc.fastgpt.cn/docs/commercial/intro/", "comon.Continue_Adding": "繼續新增", "compliance.chat": "內容由第三方 AI 產生，無法保證其真實性與準確性，僅供參考。", "compliance.dataset": "請確保您的內容嚴格遵守相關法律法規，避免包含任何違法或侵權的內容。\n在上傳可能涉及敏感資訊的資料時請務必謹慎。", "confirm_choice": "確認選擇", "confirm_move": "移動至此", "confirm_update": "確認更新", "contact_way": "通知接收", "contribute_app_template": "貢獻範本", "copy_successful": "複製成功", "copy_to_clipboard": "複製到剪貼簿", "core.Chat": "對話", "core.ai.Max context": "最大上下文", "core.ai.Model": "AI 模型", "core.ai.Not deploy rerank model": "未部署重新排名模型", "core.ai.Prompt": "提示詞", "core.ai.Support tool": "函式呼叫", "core.ai.model.Dataset Agent Model": "檔案處理模型", "core.ai.model.Vector Model": "索引模型", "core.ai.model.doc_index_and_dialog": "文件索引與對話索引", "core.app.Api request": "API 存取", "core.app.Api request desc": "透過 API 整合到現有系統中，或整合到企業微信、飛書等", "core.app.App intro": "應用程式介紹", "core.app.Auto execute": "自動執行", "core.app.Chat Variable": "對話變數", "core.app.Config schedule plan": "設定排程執行", "core.app.Config whisper": "設定語音輸入", "core.app.Config_auto_execute": "點選設定自動執行規則", "core.app.Interval timer config": "排程執行設定", "core.app.Interval timer run": "排程執行", "core.app.Interval timer tip": "可排程執行應用程式", "core.app.Make a brief introduction of your app": "為您的 AI 應用程式寫一段介紹", "core.app.Name and avatar": "頭像與名稱", "core.app.Publish": "發布", "core.app.Publish Confirm": "確認發布應用程式？這將立即更新所有發布管道的應用程式狀態。", "core.app.Publish app tip": "發布應用程式後，所有發布管道將立即使用此版本", "core.app.QG.Custom prompt tip": "為確保生成的內容遵循正確格式，【黃色部分提示詞】不允許修改", "core.app.QG.Custom prompt tip1": "為確保生成的內容遵循正確格式，", "core.app.QG.Custom prompt tip2": "【黃色部分提示詞】", "core.app.QG.Custom prompt tip3": "不允許修改", "core.app.QG.Fixed Prompt": "請嚴格遵循格式規則：以 JSON 格式返回題目：\n['問題 1'，'問題 2'，'問題 3']。", "core.app.Question Guide": "猜你想問", "core.app.Quote prompt": "引用範本提示詞", "core.app.Quote templates": "引用內容範本", "core.app.Random": "發散", "core.app.Search team tags": "搜尋標籤", "core.app.Select TTS": "選擇語音播放模式", "core.app.Select quote template": "選擇引用提示範本", "core.app.Set a name for your app": "為您的應用程式命名", "core.app.Setting ai property": "點選設定 AI 模型相關屬性", "core.app.Share link": "免登入視窗", "core.app.Share link desc": "分享連結給其他使用者，無需登入即可直接使用", "core.app.Share link desc detail": "您可以直接分享此模型給其他使用者進行對話，對方無需登入即可直接使用。請注意，此功能會消耗您帳戶的餘額，請妥善保管連結！", "core.app.TTS": "語音播放", "core.app.TTS Tip": "開啟後，每次對話後可使用語音播放功能。使用此功能可能會產生額外費用。", "core.app.TTS start": "朗讀內容", "core.app.Team tags": "團隊標籤", "core.app.Tool call": "工具呼叫", "core.app.ToolCall.No plugin": "沒有可用的外掛程式", "core.app.ToolCall.Parameter setting": "輸入參數", "core.app.ToolCall.System": "系統", "core.app.ToolCall.Team": "團隊", "core.app.Welcome Text": "對話開場白", "core.app.Whisper": "語音輸入", "core.app.Whisper config": "語音輸入設定", "core.app.deterministic": "嚴謹", "core.app.edit.Prompt Editor": "提示詞編輯器", "core.app.edit.Query extension background prompt": "對話背景描述", "core.app.edit.Query extension background tip": "描述目前對話的範圍，協助 AI 完成並擴展目前問題。填寫的內容通常是為該助手所用", "core.app.edit_content": "編輯應用程式資訊", "core.app.error.App name can not be empty": "應用程式名稱不能為空", "core.app.error.Get app failed": "取得應用程式失敗", "core.app.feedback.Custom feedback": "自訂回饋", "core.app.feedback.close custom feedback": "關閉回饋", "core.app.have_saved": "已儲存", "core.app.logs.Source And Time": "來源與時間", "core.app.more": "檢視更多", "core.app.no_app": "還沒有應用程式，快來建立一個吧！", "core.app.not_saved": "未儲存", "core.app.outLink.Can Drag": "圖示可拖曳", "core.app.outLink.Default open": "預設開啟", "core.app.outLink.Iframe block title": "複製下方的 iframe 加入到您的網站中", "core.app.outLink.Link block title": "複製下方連結在瀏覽器中開啟", "core.app.outLink.Script Close Icon": "關閉圖示", "core.app.outLink.Script Open Icon": "開啟圖示", "core.app.outLink.Script block title": "將下方程式碼加入到您的網站中", "core.app.outLink.Select Mode": "開始使用", "core.app.outLink.Select Using Way": "選擇使用方式", "core.app.outLink.Show History": "顯示歷史對話", "core.app.publish.Fei shu bot": "飛書", "core.app.publish.Fei shu bot publish": "發布到飛書機器人", "core.app.schedule.Default prompt": "預設問題", "core.app.schedule.Default prompt placeholder": "執行應用程式時的預設問題", "core.app.schedule.Every day": "每天 {{hour}}:00", "core.app.schedule.Every month": "每月 {{day}} 號 {{hour}}:00", "core.app.schedule.Every week": "每週 {{day}} {{hour}}:00", "core.app.schedule.Interval": "每 {{interval}} 小時", "core.app.schedule.Open schedule": "排程執行", "core.app.setting": "應用程式資訊設定", "core.app.share.Amount limit tip": "最多 10 組", "core.app.share.Create link": "建立新連結", "core.app.share.Create link tip": "建立成功。已複製分享網址，可直接分享使用", "core.app.share.Ip limit title": "IP 限流（人/分鐘）", "core.app.share.Is response quote": "返回引用", "core.app.share.Not share link": "尚未建立分享連結", "core.app.share.Role check": "身份驗證", "core.app.switch_to_template_market": "跳轉模板市場", "core.app.tip.Add a intro to app": "快來為應用程式寫一個介紹", "core.app.tip.chatNodeSystemPromptTip": "在此輸入提示詞", "core.app.tip.systemPromptTip": "模型固定的引導詞，透過調整此內容，可以引導模型對話方向。此內容會固定在上下文的開頭。可透過輸入 / 插入變數。\n如果關聯了知識庫，您還可以透過適當的描述，引導模型何時去呼叫知識庫搜尋。例如：\n您是電影《星際效應》的助手，當使用者詢問與《星際效應》相關的內容時，請搜尋知識庫並根據搜尋結果回答。", "core.app.tip.variableTip": "可以在對話開始前，要求使用者填寫一些內容作為本輪對話的特定變數。\n該模組位於開場引導之後。\n\n輸入框中，可透過 / 啟用變數選擇，例如：提示詞、限定詞等", "core.app.tip.welcomeTextTip": "每次對話開始前，傳送一段初始內容。支援標準 Markdown 語法。可使用的額外標記：\n[快速按鍵]：使用者點選後可以直接傳送該問題", "core.app.tool_label.doc": "使用文件", "core.app.tool_label.github": "GitHub 網址", "core.app.tool_label.price": "收費說明", "core.app.tool_label.view_doc": "檢視說明文件", "core.app.tts.Speech model": "語音模型", "core.app.tts.Speech speed": "語速", "core.app.tts.Test Listen": "測試聆聽", "core.app.tts.Test Listen Text": "您好，這是語音測試。如果您能聽到這句話，表示語音播放功能正常", "core.app.whisper.Auto send": "自動傳送", "core.app.whisper.Auto send tip": "語音輸入完成後自動傳送，無需手動點選傳送按鈕", "core.app.whisper.Auto tts response": "自動語音回應", "core.app.whisper.Auto tts response tip": "透過語音輸入傳送的問題，將直接以語音形式回應。請確保已開啟語音播放功能。", "core.app.whisper.Close": "關閉", "core.app.whisper.Not tts tip": "您尚未開啟語音播放，此功能無法使用", "core.app.whisper.Open": "開啟", "core.app.whisper.Switch": "開啟語音輸入", "core.chat.Admin Mark Content": "已修正的回覆", "core.chat.Audio Not Support": "裝置不支援語音播放", "core.chat.Audio Speech Error": "語音播放錯誤", "core.chat.Cancel Speak": "取消語音輸入", "core.chat.Confirm to clear history": "確認清除此應用程式的線上聊天記錄？分享和 API 呼叫的記錄不會被清除。", "core.chat.Confirm to clear share chat history": "確認刪除所有對話記錄？", "core.chat.Converting to text": "正在轉換為文字...", "core.chat.Custom History Title": "自訂歷史記錄標題", "core.chat.Custom History Title Description": "如果設定為空，將自動跟隨對話記錄。", "core.chat.Exit Chat": "離開對話", "core.chat.Failed to initialize chat": "初始化對話失敗", "core.chat.Feedback Failed": "送出回饋失敗", "core.chat.Feedback Modal": "結果回饋", "core.chat.Feedback Modal Tip": "輸入您對回答不滿意的部分", "core.chat.Feedback Submit": "送出回饋", "core.chat.Feedback Success": "回饋成功！", "core.chat.Finish Speak": "語音輸入完成", "core.chat.History": "歷史記錄", "core.chat.History Amount": "{{amount}} 筆記錄", "core.chat.Mark": "標記預期回答", "core.chat.Mark Description": "目前標記功能為測試版。\n\n點選新增標記後，需要選擇一個知識庫來儲存標記資料。您可以透過此功能快速標記問題和預期回答，以引導模型下次的回答。\n\n目前，標記功能與知識庫中的其他資料一樣，會受到模型的影響，不保證標記後一定 100% 符合預期。\n\n標記資料僅單向與知識庫同步。如果知識庫修改了標記資料，日誌中顯示的標記資料將無法同步。", "core.chat.Mark Description Title": "標記功能介紹", "core.chat.New Chat": "新對話", "core.chat.Pin": "釘選", "core.chat.Question Guide": "猜你想問", "core.chat.Quote": "引用", "core.chat.Quote Amount": "知識庫引用（{{amount}} 筆）", "core.chat.Read Mark Description": "檢視標記功能介紹", "core.chat.Recent use": "最近使用", "core.chat.Record": "語音輸入", "core.chat.Restart": "重新開始對話", "core.chat.Run test": "執行預覽", "core.chat.Select dataset": "選擇知識庫", "core.chat.Select dataset Desc": "選擇一個知識庫來儲存預期回答", "core.chat.Send Message": "傳送", "core.chat.Speaking": "我在聽，請說...", "core.chat.Start Chat": "開始對話", "core.chat.Type a message": "輸入問題，按 [Enter] 傳送 / 按 [Ctrl(Alt/Shift) + Enter] 換行", "core.chat.Unpin": "取消釘選", "core.chat.You need to a chat app": "您沒有可用的應用程式", "core.chat.error.Chat error": "對話發生錯誤", "core.chat.error.Messages empty": "API 內容為空，可能是文字過長", "core.chat.error.Select dataset empty": "您尚未選擇知識庫", "core.chat.error.User input empty": "使用者問題輸入為空", "core.chat.error.data_error": "取得資料錯誤", "core.chat.feedback.Close User Like": "使用者表示贊同\n點選關閉此標記", "core.chat.feedback.Feedback Close": "關閉回饋", "core.chat.feedback.No Content": "使用者未提供具體回饋內容", "core.chat.feedback.Read User dislike": "使用者表示反對\n點選檢視內容", "core.chat.logs.api": "API 呼叫", "core.chat.logs.feishu": "飛書", "core.chat.logs.free_login": "免登入連結", "core.chat.logs.mcp": "MCP 調用", "core.chat.logs.official_account": "官方帳號", "core.chat.logs.online": "線上使用", "core.chat.logs.share": "外部連結呼叫", "core.chat.logs.team": "團隊空間對話", "core.chat.logs.test": "測試", "core.chat.logs.wecom": "企業微信", "core.chat.markdown.Edit Question": "編輯問題", "core.chat.markdown.Quick Question": "點我立即發問", "core.chat.markdown.Send Question": "傳送問題", "core.chat.module_unexist": "執行失敗：應用遺失元件", "core.chat.quote.Quote Tip": "此處僅顯示實際引用內容，若資料有更新，此處不會即時更新", "core.chat.quote.Read Quote": "檢視引用", "core.chat.quote.afterUpdate": "更新後", "core.chat.quote.beforeUpdate": "更新前", "core.chat.response.Complete Response": "完整回應", "core.chat.response.Extension model": "問題最佳化模型", "core.chat.response.Read complete response": "檢視詳細資料", "core.chat.response.Read complete response tips": "點選檢視詳細流程", "core.chat.response.Tool call input tokens": "工具呼叫輸入 Token 消耗", "core.chat.response.Tool call output tokens": "工具呼叫輸出 Token 消耗", "core.chat.response.Tool call tokens": "工具呼叫 Token 消耗", "core.chat.response.context total length": "上下文總長度", "core.chat.response.loop_input": "輸入陣列", "core.chat.response.loop_input_element": "輸入陣列元素", "core.chat.response.loop_output": "輸出陣列", "core.chat.response.loop_output_element": "輸出陣列元素", "core.chat.response.module cq": "問題分類列表", "core.chat.response.module cq result": "分類結果", "core.chat.response.module extract description": "提取背景描述", "core.chat.response.module extract result": "提取結果", "core.chat.response.module historyPreview": "記錄預覽（僅顯示部分內容）", "core.chat.response.module http result": "回應內容", "core.chat.response.module if else Result": "條件判斷結果", "core.chat.response.module limit": "單次搜尋上限", "core.chat.response.module maxToken": "最大回應 Token 數", "core.chat.response.module model": "模型", "core.chat.response.module name": "模型名稱", "core.chat.response.module query": "問題/搜尋詞", "core.chat.response.module similarity": "相似度", "core.chat.response.module temperature": "溫度", "core.chat.response.module time": "執行時長", "core.chat.response.plugin output": "外掛程式輸出值", "core.chat.response.search using reRank": "結果重新排名", "core.chat.response.text output": "文字輸出", "core.chat.response.update_var_result": "變數更新結果（依序顯示多個變數更新結果）", "core.chat.response.user_select_result": "使用者選擇結果", "core.chat.retry": "重新產生", "core.chat.tts.Stop Speech": "停止", "core.dataset.Choose Dataset": "關聯知識庫", "core.dataset.Collection": "資料集", "core.dataset.Create dataset": "建立一個{{name}}", "core.dataset.Dataset": "知識庫", "core.dataset.Dataset ID": "知識庫 ID", "core.dataset.Delete Confirm": "確認刪除此知識庫？刪除後資料無法復原，請確認！", "core.dataset.Empty Dataset": "空資料集", "core.dataset.Empty Dataset Tips": "還沒有知識庫，快來建立一個吧！", "core.dataset.Folder placeholder": "這是一個目錄", "core.dataset.Intro Placeholder": "這個知識庫還沒有介紹", "core.dataset.My Dataset": "我的知識庫", "core.dataset.Query extension intro": "開啟問題最佳化功能，可以提高連續對話時知識庫搜尋的準確度。開啟此功能後，在進行知識庫搜尋時，系統會根據對話記錄，利用 AI 補充問題中缺少的資訊。", "core.dataset.Quote Length": "引用內容長度", "core.dataset.Read Dataset": "檢視知識庫詳細資料", "core.dataset.Set Website Config": "開始設定", "core.dataset.Start export": "已開始匯出", "core.dataset.Text collection": "文字資料集", "core.dataset.apiFile": "API 檔案", "core.dataset.collection.Click top config website": "點選設定網站", "core.dataset.collection.Collection raw text": "資料集內容", "core.dataset.collection.Empty Tip": "資料集是空的", "core.dataset.collection.QA Prompt": "問答拆分提示詞", "core.dataset.collection.Start Sync Tip": "確認開始同步資料？將會刪除舊資料後重新取得，請確認！", "core.dataset.collection.Sync": "同步資料", "core.dataset.collection.Sync Collection": "資料同步", "core.dataset.collection.Website Empty Tip": "還沒有關聯網站", "core.dataset.collection.Website Link": "網站網址", "core.dataset.collection.id": "集合 ID", "core.dataset.collection.metadata.Createtime": "建立時間", "core.dataset.collection.metadata.Raw text length": "原始文字長度", "core.dataset.collection.metadata.Updatetime": "更新時間", "core.dataset.collection.metadata.Web page selector": "網頁選擇器", "core.dataset.collection.metadata.metadata": "中繼資料", "core.dataset.collection.metadata.read source": "檢視原始內容", "core.dataset.collection.metadata.source": "資料來源", "core.dataset.collection.metadata.source size": "來源大小", "core.dataset.collection.status.active": "已就緒", "core.dataset.collection.status.error": "訓練異常", "core.dataset.collection.sync.result.sameRaw": "內容未變更，無需更新", "core.dataset.collection.sync.result.success": "開始同步", "core.dataset.data.Data Content": "相關資料內容", "core.dataset.data.Default Index Tip": "無法編輯，預設索引會使用【相關資料內容】與【輔助資料】的文字直接產生索引。", "core.dataset.data.Edit": "編輯資料", "core.dataset.data.Empty Tip": "此集合還沒有資料", "core.dataset.data.Search data placeholder": "搜尋相關資料", "core.dataset.data.Too Long": "總長度超出上限", "core.dataset.data.Updated": "已更新", "core.dataset.data.group": "組", "core.dataset.data.unit": "筆", "core.dataset.embedding model tip": "索引模型可以將自然語言轉換成向量，用於進行語意搜尋。\n注意，不同索引模型無法一起使用。選擇索引模型後就無法修改。", "core.dataset.error.Data not found": "資料不存在或已被刪除", "core.dataset.error.Start Sync Failed": "開始同步失敗", "core.dataset.error.invalidVectorModelOrQAModel": "向量模型或問答模型錯誤", "core.dataset.error.unAuthDataset": "無權操作此知識庫", "core.dataset.error.unAuthDatasetCollection": "無權操作此資料集", "core.dataset.error.unAuthDatasetData": "無權操作此資料", "core.dataset.error.unAuthDatasetFile": "無權操作此檔案", "core.dataset.error.unCreateCollection": "無權操作此資料", "core.dataset.error.unExistDataset": "知識庫不存在", "core.dataset.error.unLinkCollection": "不是網路連結集合", "core.dataset.externalFile": "外部檔案庫", "core.dataset.file": "檔案", "core.dataset.folder": "目錄", "core.dataset.import.Chunk Range": "範圍：{{min}}~{{max}}", "core.dataset.import.Chunk Split Tip": "將文字依照特定規則進行分段處理後，轉換成可進行語意搜尋的格式，適合大多數場景。不需要呼叫模型額外處理，成本較低。", "core.dataset.import.Continue upload": "繼續上傳", "core.dataset.import.Custom prompt": "自訂提示詞", "core.dataset.import.Custom text": "自訂文字", "core.dataset.import.Custom text desc": "手動輸入一段文字作為資料集", "core.dataset.import.Data process params": "資料處理參數", "core.dataset.import.Down load csv template": "點選下載 CSV 範本", "core.dataset.import.Link name": "網路連結", "core.dataset.import.Link name placeholder": "僅支援靜態連結。如果上傳後資料為空，可能該連結無法被讀取\n每行一個，每次最多 10 個連結", "core.dataset.import.Local file": "本機檔案", "core.dataset.import.Local file desc": "上傳 PDF、TXT、DOCX 等格式的檔案", "core.dataset.import.Preview chunks": "預覽分段（最多 15 段）", "core.dataset.import.Preview raw text": "預覽原始文字（最多 3000 字）", "core.dataset.import.Process way": "處理方式", "core.dataset.import.QA Import": "問答拆分", "core.dataset.import.QA Import Tip": "依照特定規則，將文字拆分為較大的段落，呼叫 AI 為該段落產生問答。具有非常高的檢索準確度，但可能會遺失許多內容細節。", "core.dataset.import.Select file": "選擇檔案", "core.dataset.import.Select source": "選擇來源", "core.dataset.import.Source name": "來源名稱", "core.dataset.import.Sources list": "來源列表", "core.dataset.import.Start upload": "開始上傳", "core.dataset.import.Upload complete": "上傳完成", "core.dataset.import.Upload data": "確認上傳", "core.dataset.import.Upload file progress": "檔案上傳進度", "core.dataset.import.Upload status": "狀態", "core.dataset.import.Web link": "網頁連結", "core.dataset.import.Web link desc": "讀取靜態網頁內容作為資料集", "core.dataset.import.import_success": "匯入成功，請等待訓練", "core.dataset.link": "連結", "core.dataset.search.Dataset Search Params": "知識庫搜尋設定", "core.dataset.search.Empty result response": "空搜尋回應", "core.dataset.search.Filter": "搜尋篩選", "core.dataset.search.No support similarity": "僅使用結果重新排名或語意搜尋時，支援相關度篩選", "core.dataset.search.Nonsupport": "不支援", "core.dataset.search.Params Setting": "搜尋參數設定", "core.dataset.search.Quote index": "引用索引", "core.dataset.search.ReRank": "結果重新排名", "core.dataset.search.ReRank desc": "使用重新排名模型來進行二次排序，可加強綜合排名。", "core.dataset.search.Source id": "來源 ID", "core.dataset.search.Source index": "第幾個來源", "core.dataset.search.Source name": "引用來源名稱", "core.dataset.search.Using query extension": "使用問題最佳化", "core.dataset.search.mode.embedding": "語意搜尋", "core.dataset.search.mode.embedding desc": "使用向量進行文字相關性查詢", "core.dataset.search.mode.fullTextRecall": "全文檢索", "core.dataset.search.mode.fullTextRecall desc": "使用傳統的全文檢索，適合尋找特定關鍵字和主謂語的特殊資料", "core.dataset.search.mode.mixedRecall": "混合檢索", "core.dataset.search.mode.mixedRecall desc": "使用向量檢索與全文檢索的綜合結果，並使用 RRF 演算法進行排序。", "core.dataset.search.score.embedding desc": "透過計算向量之間的距離取得分數，範圍為 0 到 1。", "core.dataset.search.score.fullText": "全文檢索", "core.dataset.search.score.fullText desc": "計算相同關鍵字的分數，範圍為 0 到無限大。", "core.dataset.search.score.reRank": "結果重新排名", "core.dataset.search.score.reRank desc": "透過重新排名模型計算句子之間的關聯度，範圍為 0 到 1。", "core.dataset.search.score.rrf": "綜合排名", "core.dataset.search.score.rrf desc": "使用倒數排名融合方法，合併多個搜尋結果。", "core.dataset.search.search mode": "搜尋方式", "core.dataset.status.active": "已就緒", "core.dataset.status.syncing": "同步中", "core.dataset.status.waiting": "排隊中", "core.dataset.test.Batch test": "批次測試", "core.dataset.test.Batch test Placeholder": "選擇一個 CSV 檔案", "core.dataset.test.Search Test": "搜尋測試", "core.dataset.test.Test": "測試", "core.dataset.test.Test Result": "測試結果", "core.dataset.test.Test Text": "單一文字測試", "core.dataset.test.Test Text Placeholder": "輸入需要測試的文字", "core.dataset.test.Test params": "測試參數", "core.dataset.test.delete test history": "刪除此測試結果", "core.dataset.test.test history": "測試歷史", "core.dataset.test.test result placeholder": "測試結果將顯示在這裡", "core.dataset.test.test result tip": "根據知識庫內容與測試文字的相似度進行排序。您可以根據測試結果調整相應的文字。\n注意：測試記錄中的資料可能已經被修改。點選某筆測試資料後將顯示最新資料。", "core.dataset.training.Agent queue": "問答訓練排隊中", "core.dataset.training.Auto mode": "補充索引", "core.dataset.training.Auto mode Tip": "透過子索引以及呼叫模型產生相關問題與摘要，來增加資料區塊的語意豐富度，更有利於檢索。需要消耗更多的儲存空間並增加 AI 呼叫次數。", "core.dataset.training.Chunk mode": "分塊儲存", "core.dataset.training.Full": "預計 20 分鐘以上", "core.dataset.training.Leisure": "閒置", "core.dataset.training.QA mode": "問答對提取", "core.dataset.training.Vector queue": "索引排隊中", "core.dataset.training.Waiting": "預計 20 分鐘", "core.dataset.training.Website Sync": "網站同步", "core.dataset.training.tag": "排隊狀況", "core.dataset.website.Base Url": "根網址", "core.dataset.website.Config": "網站設定", "core.dataset.website.Config Description": "網站同步功能允許您填寫網站的根網址，系統會自動抓取相關網頁進行知識庫訓練。僅會抓取靜態網站，主要是專案文件和部落格。", "core.dataset.website.Confirm Create Tips": "確認同步此網站，同步工作將很快開始，請確認！", "core.dataset.website.Confirm Update Tips": "確認更新網站設定？將立即依照新的設定開始同步，請確認！", "core.dataset.website.Selector": "選擇器", "core.dataset.website.Selector Course": "使用教學", "core.dataset.website.Start Sync": "開始同步", "core.dataset.website.UnValid Website Tip": "您的網站可能不是靜態網站，無法同步", "core.module.Add question type": "新增問題類型", "core.module.Add_option": "新增選項", "core.module.Can not connect self": "無法連接自己", "core.module.Data Type": "資料類型", "core.module.Dataset quote.label": "知識庫引用", "core.module.Dataset quote.select": "選擇知識庫引用", "core.module.Default Value": "預設值", "core.module.Default value": "預設值", "core.module.Default value placeholder": "不填則預設回傳空字串", "core.module.Diagram": "示意圖", "core.module.Edit intro": "編輯描述", "core.module.Field Description": "欄位描述", "core.module.Field Name": "欄位名稱", "core.module.Http request props": "請求參數", "core.module.Http request settings": "請求設定", "core.module.Http timeout": "逾時時長", "core.module.Input Type": "輸入類型", "core.module.Laf sync params": "同步參數", "core.module.Max Length": "最大長度", "core.module.Max Length placeholder": "輸入文字的最大長度", "core.module.Max Value": "最大值", "core.module.Min Value": "最小值", "core.module.QueryExtension.placeholder": "例如：\n關於 Python 的介紹和使用等問題。\n目前對話與遊戲《GTA5》有關。", "core.module.Select app": "選擇應用程式", "core.module.Setting quote prompt": "設定引用提示詞", "core.module.Variable": "全域變數", "core.module.Variable Setting": "變數設定", "core.module.edit.Field Name Cannot Be Empty": "欄位名稱不能為空", "core.module.edit.Field Value Type Cannot Be Empty": "可選資料類型不能為空", "core.module.extract.Add field": "新增欄位", "core.module.extract.Enum Description": "列舉此欄位可能的值，每行一個", "core.module.extract.Enum Value": "列舉值", "core.module.extract.Field Description Placeholder": "姓名/年齡/SQL 陳述式...", "core.module.extract.Field Setting Title": "提取欄位設定", "core.module.extract.Required": "必須回傳", "core.module.extract.Required Description": "即使無法提取該欄位，也會使用預設值進行回傳", "core.module.extract.Target field": "目標欄位", "core.module.http.Add props": "新增參數", "core.module.http.AppId": "應用程式 ID", "core.module.http.ChatId": "目前對話 ID", "core.module.http.Current time": "目前時間", "core.module.http.Histories": "歷史記錄", "core.module.http.Key already exists": "鍵值已存在", "core.module.http.Key cannot be empty": "參數名稱不能為空", "core.module.http.Props name": "參數名稱", "core.module.http.Props tip": "您可以設定 HTTP 請求的相關參數\n可透過 {{key}} 呼叫全域變數或外部變數，目前可使用的變數：\n{{variable}}", "core.module.http.Props value": "參數值", "core.module.http.ResponseChatItemId": "AI 回應的 ID", "core.module.http.Url and params have been split": "路徑參數已自動加入 Params 中", "core.module.http.curl import": "匯入 cURL", "core.module.http.curl import placeholder": "請輸入 cURL 格式內容，將提取第一個介面的請求資訊。", "core.module.input.Add Branch": "新增分支", "core.module.input.add": "新增條件", "core.module.input.description.Background": "您可以新增一些特定內容的介紹，以更好地識別使用者的問題類型。這個內容通常是介紹一個模型不知道的內容。", "core.module.input.description.HTTP Dynamic Input": "接收前一個節點的輸出值作為變數，這些變數可以被 HTTP 請求參數使用。", "core.module.input.description.Http Request Header": "自訂請求標頭，請嚴格填寫 JSON 字串。\n1. 確保最後一個屬性沒有逗號\n2. 確保鍵值包含雙引號\n例如：{\"Authorization\":\"Bearer xxx\"}", "core.module.input.description.Http Request Url": "新的 HTTP 請求網址。如果出現兩個「請求網址」，您可以刪除此模組並重新新增，以取得最新的模組設定。", "core.module.input.description.Response content": "您可以使用 \\n 來達成連續換行。\n可以透過外部模組輸入來實現回覆，外部模組輸入時會覆蓋目前填寫的內容。\n如果傳入非字串類型資料將會自動轉換為字串", "core.module.input.label.Background": "背景知識", "core.module.input.label.Http Request Url": "請求網址", "core.module.input.label.Response content": "回覆內容", "core.module.input.label.Select dataset": "選擇知識庫", "core.module.input.label.aiModel": "AI 模型", "core.module.input.label.chat history": "對話記錄", "core.module.input.label.user question": "使用者問題", "core.module.input.placeholder.Classify background": "例如：\n1. AIGC（人工智慧生成內容）是指使用人工智慧技術自動或半自動產生數位內容，如文字、影像、音樂、影片等。\n2. AIGC 技術包括但不限於自然語言處理、電腦視覺、機器學習和深度學習。這些技術可以創造新內容或修改現有內容，以滿足特定的創意、教育、娛樂或資訊需求。", "core.module.input_description": "輸入描述", "core.module.input_form": "輸入表單", "core.module.input_name": "輸入名稱", "core.module.input_type": "輸入類型", "core.module.laf.Select laf function": "選擇 LAF 函式", "core.module.output.description.Ai response content": "將在串流回覆完成後觸發", "core.module.output.description.New context": "將本次回覆內容與歷史記錄組合，作為新的上下文回傳", "core.module.output.description.query extension result": "以字串陣列形式輸出，可直接連接到「知識庫搜尋」的「使用者問題」。建議不要連接到「AI 對話」的「使用者問題」", "core.module.output.label.Ai response content": "AI 回覆內容", "core.module.output.label.New context": "新的上下文", "core.module.output.label.query extension result": "最佳化結果", "core.module.template.AI function": "AI 功能", "core.module.template.AI response switch tip": "如果您希望目前節點不輸出內容，可以關閉此開關。AI 輸出的內容不會顯示給使用者，您可以手動使用「AI 回覆內容」進行特殊處理。", "core.module.template.AI support tool tip": "支援函式呼叫的模型可以更好地使用工具呼叫。", "core.module.template.Basic Node": "基本功能", "core.module.template.Query extension": "問題最佳化", "core.module.template.System Plugin": "系統外掛", "core.module.template.System input module": "系統輸入模組", "core.module.template.Team app": "團隊應用程式", "core.module.template.Tool module": "工具", "core.module.template.UnKnow Module": "未知模組", "core.module.template.ai_chat": "AI 對話", "core.module.template.ai_chat_intro": "AI 大型模型對話", "core.module.template.config_params": "可以設定應用程式的系統參數", "core.module.template.empty_plugin": "空白外掛程式", "core.module.template.empty_workflow": "空白工作流程", "core.module.template.self_input": "外掛程式輸入", "core.module.template.self_output": "外掛程式輸出", "core.module.template.system_config": "系統設定", "core.module.template.system_config_info": "可以設定應用程式的系統參數", "core.module.template.work_start": "流程開始", "core.module.templates.Load plugin error": "載入外掛程式失敗", "core.module.variable add option": "新增選項", "core.module.variable.Custom type": "自訂變數", "core.module.variable.add option": "新增選項", "core.module.variable.input type": "文字", "core.module.variable.key": "變數鍵值", "core.module.variable.key already exists": "鍵值已存在", "core.module.variable.key is required": "變數鍵值為必填", "core.module.variable.select type": "下拉單選", "core.module.variable.text max length": "最大長度", "core.module.variable.textarea type": "段落", "core.module.variable.variable name is required": "變數名稱不能為空", "core.module.variable.variable option is required": "選項不能全部為空", "core.module.variable.variable option is value is required": "選項內容不能為空", "core.module.variable.variable options": "選項", "core.plugin.Custom headers": "自訂請求標頭", "core.plugin.Free": "此外掛程式不需消耗點數", "core.plugin.Get Plugin Module Detail Failed": "取得外掛程式資訊失敗", "core.plugin.Http plugin intro placeholder": "僅供展示，無實際效果", "core.plugin.cost": "點數消耗：", "core.tip.leave page": "內容已修改，確認離開頁面嗎？", "core.view_chat_detail": "檢視對話詳細資料", "core.workflow.Can not delete node": "此節點不允許刪除", "core.workflow.Change input type tip": "修改輸入類型將清空已填寫的值，請確認！", "core.workflow.Check Failed": "工作流校驗失敗，請檢查是否遺失、缺值，連線是否正常", "core.workflow.Confirm stop debug": "確認停止除錯？除錯資訊將不會保留。", "core.workflow.Copy node": "已複製節點", "core.workflow.Custom inputs": "自訂輸入", "core.workflow.Custom outputs": "自訂輸出", "core.workflow.Dataset quote": "知識庫引用", "core.workflow.Debug": "除錯", "core.workflow.Debug Node": "除錯模式", "core.workflow.Failed": "執行失敗", "core.workflow.Not intro": "此節點沒有介紹", "core.workflow.Run": "執行", "core.workflow.Running": "執行中", "core.workflow.Save and publish": "儲存並發布", "core.workflow.Save to cloud": "僅儲存", "core.workflow.Skipped": "跳過執行", "core.workflow.Stop debug": "停止除錯", "core.workflow.Success": "執行成功", "core.workflow.Value type": "資料類型", "core.workflow.debug.Done": "除錯完成", "core.workflow.debug.Hide result": "隱藏結果", "core.workflow.debug.Not result": "無執行結果", "core.workflow.debug.Run result": "執行結果", "core.workflow.debug.Show result": "顯示結果", "core.workflow.dynamic_input": "動態輸入", "core.workflow.inputType.JSON Editor": "JSON 輸入框", "core.workflow.inputType.Manual input": "手動輸入", "core.workflow.inputType.Manual select": "手動選擇", "core.workflow.inputType.Reference": "變數引用", "core.workflow.inputType.custom": "自訂變數", "core.workflow.inputType.dynamicTargetInput": "動態外部資料", "core.workflow.inputType.input": "單行輸入框", "core.workflow.inputType.number input": "數字輸入框", "core.workflow.inputType.select": "單選框", "core.workflow.inputType.selectApp": "應用程式選擇", "core.workflow.inputType.selectDataset": "知識庫選擇", "core.workflow.inputType.selectLLMModel": "對話模型選擇", "core.workflow.inputType.switch": "開關", "core.workflow.inputType.textInput": "文字輸入框", "core.workflow.inputType.textarea": "多行輸入框", "core.workflow.publish.OnRevert version": "點選回復至此版本", "core.workflow.publish.OnRevert version confirm": "確認回復至此版本？將為您儲存編輯中版本的設定，並為回復版本建立一個新的發布版本。", "core.workflow.publish.histories": "發布記錄", "core.workflow.template.Interactive": "互動", "core.workflow.template.Multimodal": "多模態", "core.workflow.template.Search": "搜尋", "core.workflow.tool.Handle": "工具聯結器", "core.workflow.tool.Select Tool": "選擇工具", "core.workflow.variable": "變數", "create": "建立", "create_failed": "建立失敗", "create_success": "建立成功", "create_time": "建立時間", "cron_job_run_app": "排程任務", "custom_title": "自訂標題", "data_index_custom": "自定義索引", "data_index_default": "預設索引", "data_index_question": "推測問題索引", "data_index_summary": "摘要索引", "data_not_found": "數據找不到了", "dataset.Confirm move the folder": "確認移動到此目錄", "dataset.Confirm to delete the data": "確認刪除此資料？", "dataset.Confirm to delete the file": "確認刪除此檔案及其所有資料？", "dataset.Create Folder": "建立資料夾", "dataset.Create manual collection": "建立手動資料集", "dataset.Delete Dataset Error": "刪除知識庫錯誤", "dataset.Edit Folder": "編輯資料夾", "dataset.Edit Info": "編輯資訊", "dataset.Export": "匯出", "dataset.Export Dataset Limit Error": "匯出資料失敗", "dataset.Folder Name": "輸入資料夾名稱", "dataset.Insert Data": "插入", "dataset.Manual collection Tip": "手動資料集允許建立一個空的容器來存放資料", "dataset.Move Failed": "移動錯誤", "dataset.Select Dataset": "選擇此知識庫", "dataset.Select Dataset Tips": "僅能選擇相同索引模型的知識庫", "dataset.Select Folder": "進入資料夾", "dataset.Training Name": "資料訓練", "dataset.collections.Collection Embedding": "{{total}} 個索引", "dataset.collections.Confirm to delete the folder": "確認刪除此資料夾及其所有內容？", "dataset.collections.Create And Import": "建立或匯入", "dataset.collections.Select Collection": "選擇檔案", "dataset.collections.Select One Collection To Store": "選擇一個檔案進行儲存", "dataset.data.Can not edit": "無編輯權限", "dataset.data.Default Index": "預設索引", "dataset.data.Delete Tip": "確認刪除此資料？", "dataset.data.Index Placeholder": "輸入索引文字內容", "dataset.data.Input Success Tip": "匯入資料成功", "dataset.data.Update Success Tip": "更新資料成功", "dataset.data.edit.Index": "資料索引（{{amount}}）", "dataset.data.edit.divide_content": "分割內容", "dataset.data.input is empty": "資料內容不能為空", "dataset.dataset_name": "知識庫名稱", "dataset.deleteFolderTips": "確認刪除此資料夾及其包含的所有知識庫？刪除後資料無法復原，請確認！", "dataset.test.noResult": "搜尋結果為空", "dataset_data_input_a": "答案", "dataset_data_input_chunk": "常規模式", "dataset_data_input_chunk_content": "內容", "dataset_data_input_q": "問題", "dataset_data_input_qa": "QA 模式", "dataset_text_model_tip": "用於知識庫預處理階段的文字處理，例如自動補充索引、問答對提取。", "deep_rag_search": "深度搜尋", "delete_api": "確認刪除此 API 金鑰？\n刪除後該金鑰將立即失效，對應的對話記錄不會被刪除，請確認！", "delete_failed": "刪除失敗", "delete_folder": "刪除資料夾", "delete_success": "刪除成功", "delete_warning": "刪除警告", "embedding_model_not_config": "偵測到沒有可用的索引模型", "enable_auth": "啟用鑑權", "error.Create failed": "建立失敗", "error.code_error": "驗證碼錯誤", "error.fileNotFound": "找不到檔案", "error.inheritPermissionError": "繼承權限錯誤", "error.invalid_params": "參數無效", "error.missingParams": "參數不足", "error.send_auth_code_too_frequently": "請勿頻繁取得驗證碼", "error.too_many_request": "請求太頻繁了，請稍後重試", "error.unKnow": "發生未預期的錯誤", "error.upload_file_error_filename": "{{name}} 上傳失敗", "error.upload_image_error": "上傳文件失敗", "error.username_empty": "帳號不能為空", "error_collection_not_exist": "集合不存在", "error_embedding_not_config": "未設定索引模型", "error_invalid_resource": "無效的資源", "error_llm_not_config": "未設定文件理解模型", "error_un_permission": "無權操作", "error_vlm_not_config": "未設定圖片理解模型", "exit_directly": "直接離開", "expired_time": "到期時間", "export_to_json": "匯出為 JSON", "extraction_results": "提取結果", "failed": "失敗", "field_name": "欄位名稱", "folder.empty": "此目錄中沒有更多項目了", "folder.open_dataset": "開啟知識庫", "folder_description": "資料夾描述", "free": "免費", "get_QR_failed": "取得 QR Code 失敗", "get_app_failed": "取得應用程式失敗", "get_laf_failed": "取得 LAF 函式清單失敗", "had_auth_value": "已填寫", "has_verification": "已驗證，點選解除綁定", "have_done": "已完成", "import_failed": "匯入失敗", "import_success": "匯入成功", "info.buy_extra": "購買額外方案", "info.csv_download": "點選下載批次測試範本", "info.csv_message": "讀取 CSV 檔案的第一欄進行批次測試，每次最多支援 100 組資料。", "info.felid_message": "欄位鍵必須是純英文或數字且不能以數字開頭。", "info.free_plan": "若免費版團隊連續 30 天未登入系統，系統將自動清除帳號知識庫。", "info.include": "包含標準方案與額外資源包", "info.node_info": "調整此模組會影響工具呼叫的時機。\n您可以透過精確描述此模組功能，引導模型進行工具呼叫。", "info.old_version_attention": "偵測到您的進階編排為舊版本，系統將自動格式化為新版工作流程。\n\n由於版本差異較大，可能導致某些工作流程無法正常排列。請重新手動連接工作流程。如果仍然異常，請嘗試刪除對應節點後重新新增。\n\n您可以直接點選除錯來測試工作流程。除錯完成後，點選發布。直到您點選發布，新工作流程才會真正儲存生效。\n\n在您發布新工作流程之前，自動儲存將不會生效。", "info.open_api_notice": "您可以填寫 OpenAI/OneAPI 的相關金鑰。如果您填寫了此內容，在線上平臺使用【AI 對話】、【問題分類】和【內容提取】將使用您填寫的金鑰，不會計費。請確認您的金鑰是否有存取對應模型的權限。GPT 模型可以選擇 FastAI。", "info.open_api_placeholder": "請求網址，預設為 OpenAI 官方。可填寫中轉網址，未自動補全 \"v1\"", "info.resource": "資源使用量", "input.Repeat Value": "重複的值", "input_name": "輸入名稱", "invalid_variable": "無效變數", "is_open": "是否開啟", "is_requesting": "請求中...", "is_using": "使用中", "item_description": "欄位描述", "item_name": "欄位名稱", "json_config": "JSON 設定", "json_parse_error": "可能有 JSON 錯誤，請仔細檢查", "just_now": "剛剛", "key": "鍵", "key_repetition": "鍵值重複", "last_step": "上一步", "last_use_time": "最後使用時間", "link.UnValid": "無效的連結", "llm_model_not_config": "偵測到沒有可用的語言模型", "load_failed": "載入失敗", "max_quote_tokens": "引用上限", "max_quote_tokens_tips": "單次搜尋最大的 token 數量，中文約 1 字=1.7 tokens，英文約 1 字=1 token", "mcp_server": "MCP 服務", "min_similarity": "最低相關度", "min_similarity_tip": "不同索引模型的相關度有區別，請透過搜尋測試來選擇合適的數值。\n使用 結果重排 時，使用重排結果過濾。", "model.billing": "模型計費", "model.model_type": "模型類型", "model.name": "模型名", "model.provider": "模型提供者", "model.search_name_placeholder": "根據模型名搜尋", "model.type.chat": "語言模型", "model.type.embedding": "索引模型", "model.type.reRank": "重排模型", "model.type.stt": "語音辨識", "model.type.tts": "語音合成", "model_alicloud": "阿里雲", "model_baai": "智源", "model_baichuan": "百川智能", "model_chatglm": "ChatGLM", "model_doubao": "豆包", "model_ernie": "文心一言", "model_hunyuan": "騰訊混元", "model_intern": "書生", "model_moka": "Moka-AI", "model_moonshot": "月之暗面", "model_other": "其他", "model_ppio": "PPIO 派歐雲", "model_qwen": "阿里千問", "model_siliconflow": "矽基流動", "model_sparkdesk": "訊飛星火", "model_stepfun": "階躍星辰", "model_yi": "零一萬物", "month": "月", "move.confirm": "確認移動", "move_success": "移動成功", "move_to": "移動至", "name": "名稱", "name_is_empty": "名稱不能為空", "navbar.Account": "帳戶", "navbar.Chat": "對話", "navbar.Datasets": "知識庫", "navbar.Studio": "工作區", "navbar.Toolkit": "工具箱", "navbar.Tools": "工具", "new_create": "建立新項目", "next_step": "下一步", "no": "否", "no_child_folder": "無子目錄，放置在此", "no_intro": "暫無介紹", "no_laf_env": "系統未設定 LAF 環境", "no_more_data": "沒有更多資料了", "no_pay_way": "系統無合適的支付渠道", "no_select_data": "沒有可選擇的資料", "not_model_config": "未設定相關模型", "not_open": "未開啟", "not_permission": "當前訂閱套餐不支持團隊操作日誌", "not_support": "不支援", "not_support_wechat_image": "這是一張微信圖片", "not_yet_introduced": "暫無介紹", "open_folder": "開啟資料夾", "option": "選項", "page_center": "頁面置中", "pay.amount": "金額", "pay.error_desc": "轉換支付途徑時出現了問題", "pay.noclose": "支付完成後，請等待系統自動更新", "pay.package_tip.buy": "您購買的方案等級低於目前方案，該方案將在目前方案過期後生效。\n您可在帳戶 - 個人資訊 - 方案詳細資訊中檢視方案使用情況。", "pay.package_tip.renewal": "您正在續約方案。您可在帳戶 - 個人資訊 - 方案詳細資訊中檢視方案使用情況。", "pay.package_tip.upgrade": "您購買的方案等級高於目前方案，該方案將立即生效，目前方案將延後生效。您可在帳戶 - 個人資訊 - 方案詳細資訊中檢視方案使用情況。", "pay.wechat": "請微信掃碼付款：{{price}}元\n\n付款完成前，請勿關閉頁面", "pay.wx_payment": "微信支付", "pay.yuan": "{{amount}} 元", "pay_alipay_payment": "支付寶支付", "pay_corporate_payment": "對公支付", "pay_money": "應付金額", "pay_success": "支付成功", "pay_year_tip": "支付 10 個月，暢享 1 年！", "permission.Collaborator": "協作者", "permission.Default permission": "預設權限", "permission.Manage": "管理", "permission.No InheritPermission": "已限制權限，不再繼承上層資料夾的權限", "permission.Not collaborator": "無協作者", "permission.Owner": "擁有者", "permission.Permission": "權限", "permission.Permission config": "權限設定", "permission.Private": "私人", "permission.Private Tip": "僅自己可用", "permission.Public": "團隊", "permission.Public Tip": "所有團隊用戶可用", "permission.Remove InheritPermission Confirm": "此操作會導致權限繼承失效，是否繼續？", "permission.Resume InheritPermission Confirm": "要恢復繼承上層資料夾的權限嗎？", "permission.Resume InheritPermission Failed": "恢復失敗", "permission.Resume InheritPermission Success": "恢復成功", "permission.change_owner": "轉移擁有權", "permission.change_owner_failed": "轉移擁有權失敗", "permission.change_owner_placeholder": "輸入使用者名稱以搜尋帳戶", "permission.change_owner_success": "擁有權轉移成功", "permission.change_owner_tip": "轉移後您的權限將不會保留", "permission.change_owner_to": "轉移給", "permission.manager": "管理員", "permission.read": "讀取權限", "permission.write": "寫入權限", "please_input_name": "請輸入名稱", "plugin.App": "選擇應用程式", "plugin.Currentapp": "目前應用程式", "plugin.Description": "描述", "plugin.Edit Http Plugin": "編輯 HTTP 外掛程式", "plugin.Enter PAT": "請輸入個人存取權杖（PAT）", "plugin.Get Plugin Module Detail Failed": "取得外掛程式資訊失敗", "plugin.Import Plugin": "匯入 HTTP 外掛程式", "plugin.Import from URL": "從網址匯入。https://xxxx", "plugin.Intro": "外掛程式介紹", "plugin.Invalid Env": "無效的 LAF 環境", "plugin.Invalid Schema": "無效的結構", "plugin.Invalid URL": "無效的網址", "plugin.Method": "方法", "plugin.Path": "路徑", "plugin.Please bind laf accout first": "請先綁定 LAF 帳戶", "plugin.Plugin List": "外掛程式列表", "plugin.Search plugin": "搜尋外掛程式", "plugin.Search_app": "搜尋應用程式", "plugin.Set Name": "為外掛程式命名", "plugin.contribute": "貢獻外掛程式", "plugin.go to laf": "前往編寫", "plugin.path": "路徑", "price_over_wx_limit": "超出支付提供商限額：微信支付僅支持 6000 元以下", "prompt_input_placeholder": "請輸入提示詞", "psw_inconsistency": "兩次密碼不一致", "question_feedback": "工單諮詢", "read_course": "閱讀教學", "read_doc": "閱讀文件", "read_quote": "檢視引用", "redo_tip": "重做  ctrl  shift  z", "redo_tip_mac": "重做  ⌘  shift  z", "request_end": "已載入全部", "request_error": "請求錯誤", "request_more": "點選載入更多", "required": "必填", "rerank_weight": "重排權重", "resume_failed": "恢復失敗", "root_folder": "根目錄", "save_failed": "儲存失敗", "save_success": "儲存成功", "scan_code": "掃碼支付", "secret_tips": "值保存後不會再次明文返回", "select_file_failed": "選擇檔案失敗", "select_reference_variable": "選擇引用變數", "select_template": "選擇範本", "set_avatar": "點選設定頭像", "share_link": "分享連結", "speech_error_tip": "語音轉文字失敗", "speech_not_support": "您的瀏覽器不支援語音輸入", "submit_failed": "送出失敗", "submit_success": "送出成功", "submitted": "已送出", "support": "支援", "support.account.Individuation": "個人化", "support.inform.Read": "已讀", "support.openapi.Api baseurl": "API 根網址", "support.openapi.Api manager": "API 金鑰管理", "support.openapi.Copy success": "已複製 API 網址", "support.openapi.New api key": "新的 API 金鑰", "support.openapi.New api key tip": "請妥善保管您的金鑰，金鑰將不會再次顯示", "support.outlink.Delete link tip": "確認刪除此免登入連結？刪除後，該連結將立即失效，對話記錄仍會保留，請確認！", "support.outlink.Max usage points": "點數上限", "support.outlink.Max usage points tip": "此連結最多允許使用多少點數，超出後將無法使用。-1 代表無限制。", "support.outlink.Usage points": "點數消耗", "support.outlink.share.Chat_quote_reader": "全文閱讀器", "support.outlink.share.Full_text tips": "允許閱讀該引用片段來源的完整資料集", "support.outlink.share.Response Quote": "回傳引用", "support.outlink.share.Response Quote tips": "在分享連結中回傳引用內容，但不允許使用者下載原始文件", "support.outlink.share.running_node": "執行節點", "support.outlink.share.show_complete_quote": "檢視原始內容", "support.outlink.share.show_complete_quote_tips": "檢視及下載完整引用文件，或跳轉至引用網站", "support.permission.Permission": "權限", "support.standard.AI Bonus Points": "AI 點數", "support.standard.due_date": "到期日期", "support.standard.storage": "儲存空間", "support.standard.type": "類型", "support.team.limit.No permission rerank": "無權使用結果重新排名，請升級您的方案", "support.user.Avatar": "頭像", "support.user.Go laf env": "點選前往 {{env}} 取得 PAT 權杖。", "support.user.Laf account course": "檢視綁定 LAF 帳戶教學。", "support.user.Laf account intro": "綁定您的 LAF 帳戶後，您可以在工作流程中使用 LAF 模組，實現線上編寫程式碼。", "support.user.Need to login": "請先登入", "support.user.Price": "計費標準", "support.user.User self info": "個人資訊", "support.user.auth.Sending Code": "正在傳送驗證碼", "support.user.auth.get_code": "取得驗證碼", "support.user.auth.get_code_again": "秒後重新取得", "support.user.captcha_placeholder": "請輸入驗證碼", "support.user.info.bind_notification_error": "綁定通知帳號異常", "support.user.info.bind_notification_hint": "請綁定通知接收帳號，確保您能正常接收套餐過期提醒等通知，保障您的服務正常運作。", "support.user.info.bind_notification_success": "綁定通知帳號成功", "support.user.info.code_required": "驗證碼不能為空", "support.user.info.notification_receiving_hint": "通知接收", "support.user.info.verification_code": "驗證碼", "support.user.inform.System message": "系統訊息", "support.user.login.Email": "電子郵件", "support.user.login.Github": "GitHub 登入", "support.user.login.Google": "Google 登入", "support.user.login.Microsoft": "Microsoft 登入", "support.user.login.Password": "密碼", "support.user.login.Password login": "密碼登入", "support.user.login.Phone": "手機登入", "support.user.login.Phone number": "手機號碼", "support.user.login.Provider error": "登入錯誤，請重試", "support.user.login.Username": "使用者名稱", "support.user.login.Wechat": "微信登入", "support.user.login.can_not_login": "無法登入，點選聯絡我們", "support.user.login.error": "登入錯誤", "support.user.login.security_failed": "安全驗證失敗", "support.user.login.wx_qr_login": "微信 QR Code 登入", "support.user.logout.confirm": "確認登出？", "support.user.team.Dataset usage": "知識庫容量", "support.user.team.Team Tags Async Success": "同步完成", "support.user.team.member": "用戶", "support.wallet.Ai point every thousand tokens": "{{points}} 點數/1K tokens", "support.wallet.Ai point every thousand tokens_input": "輸入：{{points}} 積分/1K tokens", "support.wallet.Ai point every thousand tokens_output": "輸出：{{points}} 積分/1K tokens", "support.wallet.Amount": "金額", "support.wallet.App_amount_not_sufficient": "您的應用數量已達上限，請升級套餐後繼續使用。", "support.wallet.Buy": "購買", "support.wallet.Dataset_amount_not_sufficient": "您的知識庫數量已達上限，請升級套餐後繼續使用。", "support.wallet.Dataset_not_sufficient": "您的知識庫容量不足，請先升級套餐或購買額外知識庫容量後繼續使用。", "support.wallet.Not sufficient": "您的 AI 點數不足，請先升級方案或購買額外 AI 點數後繼續使用。", "support.wallet.Plan expired time": "方案到期時間", "support.wallet.Standard Plan Detail": "方案詳細資訊", "support.wallet.Team_member_over_size": "您的團隊用戶數量已達上限，請升級套餐後繼續使用。", "support.wallet.To read plan": "檢視方案", "support.wallet.amount_0": "購買數量不能為 0", "support.wallet.apply_invoice": "申請發票", "support.wallet.bill.Number": "訂單編號", "support.wallet.bill.Status": "狀態", "support.wallet.bill.Type": "訂單類型", "support.wallet.bill.payWay.Way": "付款方式", "support.wallet.bill.payWay.alipay": "支付寶支付", "support.wallet.bill.payWay.balance": "餘額支付", "support.wallet.bill.payWay.bank": "對公支付", "support.wallet.bill.payWay.wx": "微信支付", "support.wallet.bill.status.closed": "已關閉", "support.wallet.bill.status.notpay": "未付款", "support.wallet.bill.status.refund": "已退款", "support.wallet.bill.status.success": "付款成功", "support.wallet.bill_detail": "帳單詳細資訊", "support.wallet.bill_tag.bill": "帳單紀錄", "support.wallet.bill_tag.default_header": "預設抬頭", "support.wallet.bill_tag.invoice": "發票紀錄", "support.wallet.billable_invoice": "可開立發票的帳單", "support.wallet.buy_resource": "購買資源包", "support.wallet.has_invoice": "已開立發票", "support.wallet.invoice_amount": "發票金額", "support.wallet.invoice_data.bank": "開戶銀行", "support.wallet.invoice_data.bank_account": "銀行帳號", "support.wallet.invoice_data.company_address": "公司地址", "support.wallet.invoice_data.company_phone": "公司電話", "support.wallet.invoice_data.email": "電子郵件地址", "support.wallet.invoice_data.need_special_invoice": "是否需要開立統一發票", "support.wallet.invoice_data.organization_name": "組織名稱", "support.wallet.invoice_data.unit_code": "統一編號", "support.wallet.invoice_detail": "發票詳細資訊", "support.wallet.invoice_info": "發票將在 3-7 個工作天內寄送至電子郵件信箱，請耐心等候", "support.wallet.invoicing": "開立發票", "support.wallet.moduleName.qa": "問答拆分", "support.wallet.noBill": "無帳單紀錄", "support.wallet.no_invoice": "無發票紀錄", "support.wallet.subscription.AI points": "AI 點數", "support.wallet.subscription.AI points click to read tip": "每次呼叫 AI 模型時，都會消耗一定的 AI 點數（類似於 Token）。點選可檢視詳細計算規則。", "support.wallet.subscription.AI points usage": "AI 點數使用量", "support.wallet.subscription.AI points usage tip": "每次呼叫 AI 模型時，都會消耗一定的 AI 點數。具體的計算標準可參考上方的「計費標準」", "support.wallet.subscription.Ai points": "AI 點數計算標準", "support.wallet.subscription.Current plan": "目前方案", "support.wallet.subscription.Extra ai points": "額外 AI 點數", "support.wallet.subscription.Extra dataset size": "額外知識庫容量", "support.wallet.subscription.Extra plan": "額外資源包", "support.wallet.subscription.Extra plan tip": "當標準方案不足時，您可以購買額外資源包繼續使用", "support.wallet.subscription.FAQ": "常見問題", "support.wallet.subscription.Month amount": "月數", "support.wallet.subscription.Next plan": "未來方案", "support.wallet.subscription.Stand plan level": "訂閱方案", "support.wallet.subscription.Sub plan": "訂閱方案", "support.wallet.subscription.Sub plan tip": "免費使用【{{title}}】或升級更進階的方案", "support.wallet.subscription.Team plan and usage": "方案與使用量", "support.wallet.subscription.Training weight": "訓練優先權：{{weight}}", "support.wallet.subscription.Update extra ai points": "額外 AI 點數", "support.wallet.subscription.Update extra dataset size": "額外儲存空間", "support.wallet.subscription.Upgrade plan": "升級方案", "support.wallet.subscription.ai_model": "AI 語言模型", "support.wallet.subscription.function.History store": "{{amount}} 天對話紀錄保留", "support.wallet.subscription.function.Max app": "{{amount}} 個應用上限", "support.wallet.subscription.function.Max dataset": "{{amount}} 個知識庫上限", "support.wallet.subscription.function.Max dataset size": "{{amount}} 組知識庫索引", "support.wallet.subscription.function.Max members": "{{amount}} 個團隊用戶", "support.wallet.subscription.function.Points": "{{amount}} AI 點數", "support.wallet.subscription.mode.Month": "按月", "support.wallet.subscription.mode.Period": "訂閱週期", "support.wallet.subscription.mode.Year": "按年", "support.wallet.subscription.mode.Year sale": "贈送兩個月", "support.wallet.subscription.point": "點數", "support.wallet.subscription.standardSubLevel.custom": "客製版", "support.wallet.subscription.standardSubLevel.enterprise": "企業版", "support.wallet.subscription.standardSubLevel.enterprise_desc": "適合中小企業在正式環境建構知識庫應用", "support.wallet.subscription.standardSubLevel.experience": "體驗版", "support.wallet.subscription.standardSubLevel.experience_desc": "可解鎖 FastGPT 完整功能", "support.wallet.subscription.standardSubLevel.free": "免費版", "support.wallet.subscription.standardSubLevel.free desc": "核心功能免費試用。 \n30 天未登錄，將會清空知識庫。", "support.wallet.subscription.standardSubLevel.team": "團隊版", "support.wallet.subscription.standardSubLevel.team_desc": "適合小團隊建構知識庫應用並提供對外服務", "support.wallet.subscription.status.active": "使用中", "support.wallet.subscription.status.expired": "已過期", "support.wallet.subscription.status.inactive": "未使用", "support.wallet.subscription.team_operation_log": "記錄團隊操作日誌", "support.wallet.subscription.token_compute": "點選檢視線上 Token 計算機", "support.wallet.subscription.type.balance": "餘額儲值", "support.wallet.subscription.type.extraDatasetSize": "知識庫擴充容量", "support.wallet.subscription.type.extraPoints": "AI 點數方案", "support.wallet.subscription.type.standard": "方案訂閱", "support.wallet.subscription.web_site_sync": "網站同步", "support.wallet.usage.Ai model": "AI 模型", "support.wallet.usage.App name": "應用程式名稱", "support.wallet.usage.Audio Speech": "語音播放", "support.wallet.usage.Bill Module": "計費模組", "support.wallet.usage.Duration": "時長（秒）", "support.wallet.usage.Module name": "模組名稱", "support.wallet.usage.Source": "來源", "support.wallet.usage.Text Length": "文字長度", "support.wallet.usage.Time": "產生時間", "support.wallet.usage.Token Length": "Token 長度", "support.wallet.usage.Total": "總金額", "support.wallet.usage.Total points": "AI 點數消耗", "support.wallet.usage.Usage Detail": "使用詳細資訊", "support.wallet.usage.Whisper": "語音輸入", "sync_link": "同步連結", "sync_success": "同步成功", "system.Concat us": "聯絡我們", "system.Help Document": "說明文件", "system_help_chatbot": "機器人助手", "tag_list": "標籤列表", "team_tag": "團隊標籤", "templateTags.Image_generation": "圖片生成", "templateTags.Office_services": "辦公服務", "templateTags.Roleplay": "角色扮演", "templateTags.Web_search": "聯網搜索", "templateTags.Writing": "文字創作", "template_market": "模板市場", "textarea_variable_picker_tip": "輸入「/」以選擇變數", "to_dataset": "前往知識庫", "ui.textarea.Magnifying": "放大", "un_used": "未使用", "unauth_token": "憑證已過期，請重新登入", "undo_tip": "復原  ctrl  z", "undo_tip_mac": "復原  ⌘  z ", "unit.character": "字元", "unit.minute": "分鐘", "unit.seconds": "秒", "unknow_source": "未知來源", "unusable_variable": "無可用變數", "update_failed": "更新失敗", "update_success": "更新成功", "upload_file": "上傳檔案", "upload_file_error": "上傳檔案失敗", "use_helper": "使用說明", "user.Account": "帳戶", "user.Amount of earnings": "收益（￥）", "user.Amount of inviter": "累計邀請人數", "user.Application Name": "專案名稱", "user.Avatar": "頭像", "user.Change": "變更", "user.Copy invite url": "複製邀請連結", "user.Edit name": "點選修改暱稱", "user.Invite Url": "邀請連結", "user.Invite url tip": "透過此連結註冊的朋友將永久與您綁定，其儲值時您會獲得一定餘額獎勵。\n此外，朋友使用手機號碼註冊時，您將立即獲得 5 元獎勵。\n獎勵會傳送到您的預設團隊中。", "user.Laf Account Setting": "LAF 帳戶設定", "user.Language": "語言", "user.Member Name": "暱稱", "user.No_right_to_reset_password": "沒有重置密碼的權限", "user.Notification Receive": "通知接收", "user.Notification Receive Bind": "請先綁定通知接收方式", "user.Old password is error": "舊密碼錯誤", "user.OpenAI Account Setting": "OpenAI 帳戶設定", "user.Password": "密碼", "user.Password has no change": "新密碼和舊密碼重複", "user.Pay": "儲值", "user.Promotion": "促銷", "user.Promotion Rate": "回饋比例", "user.Promotion rate tip": "朋友儲值時您將獲得一定比例的餘額獎勵", "user.Replace": "更換", "user.Set OpenAI Account Failed": "設定 OpenAI 帳戶失敗", "user.Team": "團隊", "user.Time": "時間", "user.Timezone": "時區", "user.Update Password": "更新密碼", "user.Update password failed": "更新密碼失敗", "user.Update password successful": "更新密碼成功", "user.apikey.key": "API 金鑰", "user.confirm_password": "確認密碼", "user.init_password": "請初始化密碼", "user.new_password": "新密碼", "user.no_invite_records": "無邀請紀錄", "user.no_notice": "無通知", "user.no_usage_records": "無使用紀錄", "user.old_password": "舊密碼", "user.password_message": "密碼最少 4 位最多 60 位", "user.password_tip": "密碼至少 8 位，且至少包含兩種組合：數字、字母或特殊字元", "user.reset_password": "重置密碼", "user.reset_password_tip": "未設置初始密碼/長時間未修改密碼，請重置密碼", "user.team.Balance": "團隊餘額", "user.team.Check Team": "切換", "user.team.Leave Team": "離開團隊", "user.team.Leave Team Failed": "離開團隊失敗", "user.team.Member": "用戶", "user.team.Member Name": "用戶名稱", "user.team.Over Max Member Tip": "團隊最多 {{max}} 人", "user.team.Personal Team": "個人團隊", "user.team.Processing invitations": "處理邀請", "user.team.Processing invitations Tips": "您有 {{amount}} 個需要處理的團隊邀請", "user.team.Remove Member Confirm Tip": "確認將 {{username}} 移出團隊？", "user.team.Select Team": "選擇團隊", "user.team.Switch Team Failed": "切換團隊失敗", "user.team.Tags Async": "儲存", "user.team.Team Tags Async": "標籤同步", "user.team.Team Tags Async Success": "連結錯誤修正成功，標籤資訊已更新", "user.team.invite.Accepted": "已加入團隊", "user.team.invite.Deal Width Footer Tip": "處理完會自動關閉", "user.team.invite.Reject": "已拒絕邀請", "user.team.member.Confirm Leave": "確認離開此團隊？\n離開後，您在該團隊的所有資源（應用程式、知識庫、資料夾、管理的角色等）均會轉讓給團隊擁有者。", "user.team.member.active": "已加入", "user.team.member.reject": "已拒絕", "user.team.member.waiting": "待接受", "user.team.role.Admin": "管理員", "user.team.role.Owner": "擁有者", "user.team.role.Visitor": "訪客", "user.team.role.writer": "可寫入用戶", "user.type": "類型", "user_leaved": "已離開", "value": "值", "verification": "驗證", "workflow.template.communication": "通訊", "xx_search_result": "{{key}} 的搜尋結果", "yes": "是", "yesterday": "昨天", "yesterday_detail_time": "昨天 {{time}}", "zoomin_tip": "縮小  ctrl  -", "zoomin_tip_mac": "縮小  ⌘  -", "zoomout_tip": "放大  ctrl  +", "zoomout_tip_mac": "放大  ⌘  +"}