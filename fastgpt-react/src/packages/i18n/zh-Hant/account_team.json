{"1person": "1 人", "1year": "1 年", "30mins": "30 分鐘", "7days": "7 天", "accept": "接受", "action": "操作", "admin_add_plan": "添加團隊套餐", "admin_add_user": "添加用戶", "admin_change_license": "變更許可證", "admin_create_app_template": "添加模板", "admin_create_plugin": "添加插件", "admin_create_plugin_group": "創建插件分組", "admin_delete_app_template": "刪除模板", "admin_delete_plugin": "插件刪除", "admin_delete_plugin_group": "刪除插件分組", "admin_delete_template_type": "刪除模板分類", "admin_finish_invoice": "開具發票", "admin_login": "管理員登錄", "admin_send_system_inform": "發送系統通知", "admin_update_app_template": "更新模板", "admin_update_plan": "編輯團隊套餐", "admin_update_plugin": "插件更新", "admin_update_plugin_group": "插件分組更新", "admin_update_system_config": "系統配置更新", "admin_update_system_modal": "系統公告配置", "admin_update_team": "編輯團隊信息", "admin_update_user": "編輯用戶信息", "assign_permission": "權限變更", "audit_log": "審計", "change_department_name": "組織結構編輯", "change_member_name": "用戶改名", "change_member_name_self": "變更用戶名", "change_notification_settings": "變更通知接收途徑", "change_password": "更改密碼", "confirm_delete_from_org": "確認將 {{username}} 移出組織結構？", "confirm_delete_from_team": "確認將 {{username}} 移出團隊？", "confirm_delete_group": "確認刪除角色？", "confirm_delete_org": "確認刪除該組織結構？", "confirm_forbidden": "確認停用", "confirm_leave_team": "確認離開該團隊？  \n結束後，您在該團隊所有的資源轉讓給團隊所有者。", "copy_link": "複製連結", "create_api_key": "創建api密鑰", "create_app": "創建應用", "create_app_copy": "創建應用副本", "create_app_folder": "創建應用文件夾", "create_app_publish_channel": "創建分享渠道", "create_data": "插入數據", "create_dataset": "創建知識庫", "create_dataset_folder": "創建知識庫文件夾", "create_department": "創建子組織結構", "create_group": "建立角色", "create_invitation_link": "建立邀請連結", "create_invoice": "開發票", "create_org": "建立組織結構", "create_sub_org": "建立子組織結構", "dataset.api_file": "API 匯入", "dataset.common_dataset": "知識庫", "dataset.external_file": "外部文件", "dataset.feishu_dataset": "飛書多維表格", "dataset.folder_dataset": "資料夾", "dataset.website_dataset": "網站同步", "dataset.yuque_dataset": "語雀知識庫", "delete": "刪除", "delete_api_key": "刪除api密鑰", "delete_app": "刪除工作台應用", "delete_app_collaborator": "應用權限刪除", "delete_app_publish_channel": "刪除發布渠道", "delete_collection": "刪除集合", "delete_data": "刪除數據", "delete_dataset": "刪除知識庫", "delete_dataset_collaborator": "知識庫權限刪除", "delete_department": "刪除子組織結構", "delete_from_org": "移出組織結構", "delete_from_team": "移出團隊", "delete_group": "刪除角色", "delete_org": "刪除組織結構", "department": "組織結構", "edit_info": "編輯訊息", "edit_member": "編輯使用者", "edit_member_tip": "用戶名", "edit_org_info": "編輯組織結構資訊", "expires": "過期時間", "export_app_chat_log": "導出應用聊天記錄", "export_bill_records": "導出賬單記錄", "export_dataset": "導出知識庫", "export_members": "導出用戶", "forbid_hint": "停用後，該邀請連結將失效。該操作不可撤銷，是否確認停用？", "forbid_success": "停用成功", "forbidden": "停用", "group": "角色", "group_name": "角色名稱", "handle_invitation": "處理團隊邀請", "has_forbidden": "已失效", "has_invited": "已邀請", "ignore": "忽略", "inform_level_common": "一般", "inform_level_emergency": "緊急", "inform_level_important": "重要", "invitation_copy_link": "【{{systemName}}】 {{userName}} 邀請您加入{{teamName}}團隊，連結：{{url}}", "invitation_link_auto_clean_hint": "已失效連結將在 30 天後自動清理", "invitation_link_description": "連結描述", "invitation_link_list": "連結列表", "invite_member": "邀請用戶", "invited": "已邀請", "join_team": "加入團隊", "join_update_time": "加入/更新時間", "kick_out_team": "移除用戶", "label_sync": "標籤同步", "leave": "已離職", "leave_team_failed": "離開團隊異常", "log_admin_add_plan": "【{{name}}】將給團隊id為【{{teamId}}】的團隊添加了套餐", "log_admin_add_user": "【{{name}}】創建了一個名為【{{userName}}】的用戶", "log_admin_change_license": "【{{name}}】變更了License", "log_admin_create_app_template": "【{{name}}】添加了名為【{{templateName}}】的模板", "log_admin_create_plugin": "【{{name}}】添加了名為【{{pluginName}}】的插件", "log_admin_create_plugin_group": "【{{name}}】創建了名為【{{groupName}}】的插件分組", "log_admin_delete_app_template": "【{{name}}】刪除了名為【{{templateName}}】的模板", "log_admin_delete_plugin": "【{{name}}】刪除了名為【{{pluginName}}】的插件", "log_admin_delete_plugin_group": "【{{name}}】刪除了名為【{{groupName}}】的插件分組", "log_admin_delete_template_type": "【{{name}}】刪除了名為【{{typeName}}】的模板分類", "log_admin_finish_invoice": "【{{name}}】給名為【{{teamName}}】的團隊開具了發票", "log_admin_login": "【{{name}}】登錄了管理員後台", "log_admin_save_template_type": "【{{name}}】添加了名為【{{typeName}}】的模板分類", "log_admin_send_system_inform": "【{{name}}】發送了標題為【{{informTitle}}】的系統通知，等級為【{{level}}】", "log_admin_update_app_template": "【{{name}}】更新了名為【{{templateName}}】的模板信息", "log_admin_update_plan": "【{{name}}】編輯了團隊id為【{{teamId}}】的團隊的套餐信息", "log_admin_update_plugin": "【{{name}}】更新了名為【{{pluginName}}】的插件信息", "log_admin_update_plugin_group": "【{{name}}】更新了名為【{{groupName}}】的插件分組", "log_admin_update_system_config": "【{{name}}】更新了系統配置", "log_admin_update_system_modal": "【{{name}}】進行了系統公告配置", "log_admin_update_team": "【{{name}}】將名為【{{teamName}}】的團隊編輯信息為團隊名：【{{newTeamName}}】，餘額：【{{newBalance}}】", "log_admin_update_user": "修改【{{userName}}】的用戶信息", "log_assign_permission": "【{{name}}】更新了【{{objectName}}】的權限：[應用創建:【{{appCreate}}】, 知識庫:【{{datasetCreate}}】, API密鑰:【{{apiKeyCreate}}】, 管理:【{{manage}}】]", "log_change_department": "【{{name}}】更新了組織結構【{{departmentName}}】", "log_change_member_name": "【{{name}}】將用戶【{{memberName}}】重命名為【{{newName}}】", "log_change_member_name_self": "【{{name}}】變更自己的用戶名為【{{newName}}】", "log_change_notification_settings": "【{{name}}】進行了變更通知接收途徑操作", "log_change_password": "【{{name}}】進行了變更密碼操作", "log_create_api_key": "【{{name}}】創建了名為【{{keyName}}】的api密鑰", "log_create_app": "【{{name}}】創建了名為【{{appName}}】的【{{appType}}】", "log_create_app_copy": "【{{name}}】給名為【{{appName}}】的【{{appType}}】創建了一個副本", "log_create_app_folder": "【{{name}}】創建了名為【{{folderName}}】的文件夾", "log_create_app_publish_channel": "【{{name}}】給名為【{{appName}}】的【{{appType}}】創建了名為【{{channelName}}】的渠道", "log_create_collection": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】創建了名為【{{collectionName}}】的集合", "log_create_data": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】往名為【{{collectionName}}】的集合插入數據", "log_create_dataset": "【{{name}}】創建了名為【{{datasetName}}】的【{{datasetType}}】", "log_create_dataset_folder": "【{{name}}】創建了名為{{folderName}}】的文件夾", "log_create_department": "【{{name}}】創建了組織結構【{{departmentName}}】", "log_create_group": "【{{name}}】創建了角色【{{groupName}}】", "log_create_invitation_link": "【{{name}}】創建了邀請鏈接【{{link}}】", "log_create_invoice": "【{{name}}】進行了開發票操作", "log_delete_api_key": "【{{name}}】刪除了名為【{{keyName}}】的api密鑰", "log_delete_app": "【{{name}}】將名為【{{appName}}】的【{{appType}}】刪除", "log_delete_app_collaborator": "【{{name}}】將名為【{{appName}}】的【{{appType}}】中名為【{{itemValueName}}】的【{{itemName}}】權限刪除", "log_delete_app_publish_channel": "【{{name}}】名為【{{appName}}】的【{{appType}}】刪除了名為【{{channelName}}】的渠道", "log_delete_collection": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】刪除了名為【{{collectionName}}】的集合", "log_delete_data": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】在名為【{{collectionName}}】的集合刪除數據", "log_delete_dataset": "【{{name}}】刪除了名為【{{datasetName}}】的【{{datasetType}}】", "log_delete_dataset_collaborator": "【{{name}}】將名為【{{datasetName}}】的【{{datasetType}}】中名為【itemValueName】的【itemName】權限刪除", "log_delete_department": "{{name}} 刪除了組織結構 {{departmentName}}", "log_delete_group": "{{name}} 刪除了角色 {{groupName}}", "log_details": "詳情", "log_export_app_chat_log": "【{{name}}】導出了名為【{{appName}}】的【{{appType}}】的聊天記錄", "log_export_bill_records": "【{{name}}】導出了賬單記錄", "log_export_dataset": "【{{name}}】導出了名為【{{datasetName}}】的【{{datasetType}}】", "log_join_team": "【{{name}}】通過邀請鏈接【{{link}}】加入團隊", "log_kick_out_team": "{{name}} 移除了用戶 {{memberName}}", "log_login": "【{{name}}】登錄了系統", "log_move_app": "【{{name}}】將名為【{{appName}}】的【{{appType}}】移動到【{{targetFolderName}}】", "log_move_dataset": "【{{name}}】將名為【{{datasetName}}】的【{{datasetType}}】移動到【{{targetFolderName}}】", "log_recover_team_member": "【{{name}}】恢復了用戶【{{memberName}}】", "log_relocate_department": "【{{name}}】移動了組織結構【{{departmentName}}】", "log_retrain_collection": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】重新訓練了名為【{{collectionName}}】的集合", "log_search_test": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】執行搜索測試操作", "log_set_invoice_header": "【{{name}}】進行了設置發票抬頭操作", "log_time": "操作時間", "log_transfer_app_ownership": "【{{name}}】將名為【{{appName}}】的【{{appType}}】的所有權從【{{oldOwnerName}}】轉移到【{{newOwnerName}}】", "log_transfer_dataset_ownership": "【{{name}}】將名為【{{datasetName}}】的【{{datasetType}}】的所有權從【{{oldOwnerName}}】轉移到【{{newOwnerName}}】", "log_type": "操作類型", "log_update_api_key": "【{{name}}】更新了名為【{{keyName}}】的api密鑰", "log_update_app_collaborator": "【{{name}}】將名為【{{appName}}】的【{{appType}}】的合作者更新為：組織：【{{orgList}}】，角色：【{{groupList}}】，用戶【{{tmbList}}】；權限更新為：讀權限：【{{readPermission}}】，寫權限：【{{writePermission}}】，管理員權限：【{{managePermission}}】", "log_update_app_info": "【{{name}}】更新了名為【{{appName}}】的【{{appType}}】:【{{newItemNames}}】為【{{newItemValues}}】", "log_update_app_publish_channel": "【{{name}}】給名為【{{appName}}】的【{{appType}}】更新了名為【{{channelName}}】的渠道", "log_update_collection": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】更新了名為【{{collectionName}}】的集合", "log_update_data": "【{{name}}】在名為【{{datasetName}}】的【{{datasetType}}】在名為【{{collectionName}}】的集合更新數據", "log_update_dataset": "【{{name}}】更新了名為【{{datasetName}}】的【{{datasetType}}】", "log_update_dataset_collaborator": "【{{name}}】將名為【{{datasetName}}】的【{{datasetType}}】的合作者更新為：組織：【{{orgList}}】，角色：【{{groupList}}】，用戶【{{tmbList}}】；權限更新為：【{{readPermission}}】，【{{writePermission}}】，【{{managePermission}}】", "log_update_publish_app": "【{{name}}】【{{operationName}}】名為【{{appName}}】的【{{appType}}】", "log_user": "操作人員", "login": "登入", "manage_member": "管理用戶", "member": "用戶", "member_group": "所屬用戶組", "move_app": "應用位置移動", "move_dataset": "移動知識庫", "move_member": "移動用戶", "move_org": "行動組織結構", "notification_recieve": "團隊通知接收", "org": "組織", "org_description": "介紹", "org_name": "組織結構名稱", "owner": "擁有者", "permission": "權限", "permission_apikeyCreate": "建立 API 密鑰", "permission_apikeyCreate_Tip": "可以創建全局的 APIKey和 MCP 服務", "permission_appCreate": "建立應用程式", "permission_appCreate_tip": "可以在根目錄建立應用程式，（資料夾下的建立權限由資料夾控制）", "permission_datasetCreate": "建立知識庫", "permission_datasetCreate_Tip": "可以在根目錄建立知識庫，（資料夾下的建立權限由資料夾控制）", "permission_manage": "管理員", "permission_manage_tip": "可以管理用戶、建立角色、管理所有角色、為角色和用戶分配權限", "please_bind_contact": "請綁定聯繫方式", "purchase_plan": "升級套餐", "recover_team_member": "用戶恢復", "relocate_department": "組織結構移動", "remark": "備註", "remove_tip": "確認將 {{username}} 移出團隊？", "restore_tip": "確認將 {{username}} 加入團隊嗎？\n僅恢復該用戶賬號可用性及相關權限，無法恢復賬號下資源。", "restore_tip_title": "恢復確認", "retain_admin_permissions": "保留管理員權限", "retrain_collection": "重新訓練集合", "save_and_publish": "儲存並發布", "search_log": "搜索日誌", "search_member": "搜索用戶", "search_member_group_name": "搜尋用戶/角色名稱", "search_org": "搜索組織結構", "search_test": "搜索測試", "set_invoice_header": "設置發票抬頭", "set_name_avatar": "團隊頭像", "sync_immediately": "立即同步", "sync_member_failed": "同步用戶失敗", "sync_member_success": "同步用戶成功", "total_team_members": "共 {{amount}} 名用戶", "transfer_app_ownership": "轉移應用程式所有權", "transfer_dataset_ownership": "轉移知識庫所有權", "transfer_ownership": "轉讓所有者", "type.Folder": "資料夾", "type.Http plugin": "HTTP 外掛", "type.Plugin": "外掛", "type.Simple bot": "簡易應用程式", "type.Tool": "工具", "type.Tool set": "工具集", "type.Workflow bot": "工作流程", "unlimited": "無限制", "update": "更新", "update_api_key": "更新api密鑰", "update_app_collaborator": "應用權限更改", "update_app_info": "應用信息修改", "update_app_publish_channel": "更新發布渠道", "update_collection": "更新集合", "update_data": "更新數據", "update_dataset": "更新知識庫", "update_dataset_collaborator": "知識庫權限更改", "update_publish_app": "應用更新", "used_times_limit": "有效人數", "user_name": "使用者名稱", "user_team_invite_member": "邀請用戶", "user_team_leave_team": "離開團隊", "user_team_leave_team_failed": "離開團隊失敗", "waiting": "待接受"}