{"name": "sinitek-mind", "version": "1.0.0", "description": "携宁智脑 - 大模型调度平台", "author": "SiniCube", "private": true, "type": "module", "workspaces": ["fastgpt-react"], "scripts": {"dev": "cross-env VITE_ENV_FILE=.env.local node scripts/dev-server.js", "dev:remote": "cross-env VITE_ENV_FILE=.env.development node scripts/dev-server.js", "dev:vue": "vite --force", "dev:react": "cd fastgpt-react && npm run dev", "build": "npm run build:vue && npm run build:react", "build:vue": "vue-tsc --noEmit && vite build", "build:react": "cd fastgpt-react && npm run build && npm run copy:dist", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx --fix ./src"}, "dependencies": {"@antv/x6": "1.34.5", "@antv/x6-vue-shape": "1.5.4", "bignumber.js": "9.0.0", "element-plus": "2.8.0", "jsencrypt": "3.2.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "sinitek-css": "8.0.3", "sinitek-message": "8.0.3", "sinitek-ui": "8.0.3", "sinitek-util": "8.0.3", "sinitek-workflow": "8.0.3", "sirmapp": "8.0.3", "vue": "^3.5.12", "vue-i18n": "9.2.2", "vue-router": "4.2.2", "vuex": "4.0.2"}, "devDependencies": {"@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "@vitejs/plugin-legacy": "5.3.2", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "autoprefixer": "10.4.19", "cross-env": "7.0.3", "eslint": "8.14.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-n": "15.0.0", "eslint-plugin-promise": "6.0.0", "eslint-plugin-vue": "8.7.1", "rollup-plugin-visualizer": "5.12.0", "sass": "1.51.0", "terser": "5.4.0", "typescript": "4.9.5", "vite": "5.2.10", "vite-plugin-chunk-split": "0.2.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vue-tsc": "1.8.27"}}